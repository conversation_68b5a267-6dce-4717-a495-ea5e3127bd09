# AveHub Developer Portal API Documentation

## Overview

The AveHub Developer Portal provides a comprehensive REST API for managing applications, AI interactions, file uploads, email notifications, and administrative tasks. All endpoints require authentication unless otherwise specified.

## Authentication

### Session-based Authentication
Most endpoints require a valid user session obtained through NextAuth.js authentication.

### API Key Authentication
Some endpoints support API key authentication for external access:
```
Authorization: Bearer your-api-key
```
or
```
X-API-Key: your-api-key
```

## Base URL
```
https://your-domain.com/api
```

---

## App Management API

### Create App
Creates a new application submission.

**Endpoint:** `POST /api/app/create`

**Authentication:** Required (Session)

**Request Body:**
```json
{
  "name": "My Awesome App",
  "description": "A comprehensive description of the app",
  "shortDescription": "Brief app description",
  "version": "1.0.0",
  "category": "productivity",
  "tags": ["productivity", "tools"],
  "websiteUrl": "https://myapp.com",
  "supportEmail": "<EMAIL>",
  "minVersion": "10.0",
  "maxVersion": "15.0",
  "appFile": "File object",
  "iconFile": "File object",
  "screenshots": ["File objects"]
}
```

**Response:**
```json
{
  "success": true,
  "appId": "app_123456789",
  "message": "App created successfully and is pending review"
}
```

**Error Response:**
```json
{
  "error": "Missing required fields",
  "details": ["name is required", "description is required"]
}
```

### Get App Details
Retrieves detailed information about a specific app.

**Endpoint:** `GET /api/app/[id]`

**Authentication:** Required (Session)

**Response:**
```json
{
  "success": true,
  "app": {
    "id": "app_123456789",
    "name": "My Awesome App",
    "description": "App description",
    "version": "1.0.0",
    "status": "PENDING",
    "downloadUrl": "/storage/files/my-awesome-app/app.zip",
    "fileSize": 1048576,
    "screenshots": ["/storage/files/my-awesome-app/screenshots/1.png"],
    "iconUrl": "/storage/files/my-awesome-app/icon.png",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z",
    "developer": {
      "name": "Developer Name",
      "email": "<EMAIL>"
    },
    "versions": []
  }
}
```

### Update App
Updates an existing application.

**Endpoint:** `PUT /api/app/[id]`

**Authentication:** Required (Session)

**Request Body:**
```json
{
  "description": "Updated description",
  "shortDescription": "Updated short description",
  "version": "1.1.0",
  "category": "productivity",
  "tags": ["productivity", "tools", "updated"],
  "websiteUrl": "https://myapp.com",
  "supportEmail": "<EMAIL>",
  "changelog": "Bug fixes and improvements"
}
```

**Response:**
```json
{
  "success": true,
  "app": {
    "id": "app_123456789",
    "version": "1.1.0",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  },
  "message": "App updated successfully"
}
```

### Delete App
Deletes an application and its associated files.

**Endpoint:** `DELETE /api/app/[id]`

**Authentication:** Required (Session)

**Response:**
```json
{
  "success": true,
  "message": "App deleted successfully"
}
```

### List Apps (Admin)
Retrieves a paginated list of all apps with filtering options.

**Endpoint:** `GET /api/app/list`

**Authentication:** Required (API Key + Admin)

**Query Parameters:**
- `status` (optional): Filter by status (PENDING, APPROVED, REJECTED, SUSPENDED)
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20)

**Response:**
```json
{
  "apps": [
    {
      "id": "app_123456789",
      "name": "My Awesome App",
      "status": "PENDING",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "developer": {
        "id": "user_123",
        "name": "Developer Name",
        "email": "<EMAIL>"
      }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 100,
    "pages": 5
  }
}
```

### Get User Apps
Retrieves apps belonging to the authenticated user.

**Endpoint:** `GET /api/user/apps`

**Authentication:** Required (API Key)

**Response:**
```json
{
  "apps": [
    {
      "id": "app_123456789",
      "name": "My Awesome App",
      "status": "APPROVED",
      "downloads": 1500,
      "version": "1.0.0",
      "iconUrl": "/storage/files/my-awesome-app/icon.png",
      "shortDescription": "Brief description",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ]
}
```

---

## File Upload API

### Upload App Files
Uploads files for an existing application with enhanced security validation.

**Endpoint:** `POST /api/app/[id]/upload`

**Authentication:** Required (Session)

**Content-Type:** `multipart/form-data`

**Supported File Types:**
- **Archives:** .zip, .rar, .7z (max 500MB)
- **Executables:** .exe, .msi, .dmg (max 1GB)
- **Mobile Apps:** .apk, .ipa (max 500MB)
- **Linux Packages:** .deb, .rpm, .appimage

**Request Body:**
```
appFile: File (application file)
iconFile: File (application icon)
screenshots: File[] (up to 5 screenshots)
```

**Response:**
```json
{
  "success": true,
  "uploadResults": {
    "downloadUrl": "/storage/files/my-app/app.zip",
    "fileSize": 1048576,
    "fileType": "zip",
    "originalFileName": "MyApp.zip",
    "iconUrl": "/storage/files/my-app/icon.png",
    "screenshots": [
      "/storage/files/my-app/screenshots/screenshot-1.png"
    ],
    "virusScanResult": {
      "isClean": true,
      "scanEngine": "VirusTotal",
      "scanDate": "2024-01-01T00:00:00.000Z",
      "threats": []
    }
  },
  "message": "Files uploaded successfully"
}
```

**Error Response:**
```json
{
  "error": "File validation failed",
  "details": [
    "File size exceeds maximum allowed size (500MB)",
    "File signature does not match expected format"
  ]
}
```

**Security Features:**
- File type validation using magic numbers
- Virus scanning integration (VirusTotal/ClamAV)
- File size limits per category
- Suspicious filename detection
- Temporary quarantine for infected files

---

## Avena AI API

### Chat with AI
Interact with the Avena AI assistant for platform-related queries.

**Endpoint:** `POST /api/ai/chat`

**Authentication:** Required (Session)

**Request Body:**
```json
{
  "messages": [
    {
      "role": "user",
      "content": "How can I improve my app for better approval chances?",
      "timestamp": "2024-01-01T00:00:00.000Z"
    }
  ],
  "context": {
    "userStats": {
      "totalApps": 5,
      "approvedApps": 3
    }
  }
}
```

**Response:**
```json
{
  "success": true,
  "response": {
    "message": "Here are some ways to improve your app for better approval chances:\n\n• **Clear Documentation** - Provide comprehensive descriptions and screenshots\n• **Quality Assurance** - Test thoroughly before submission\n• **Follow Guidelines** - Ensure compliance with platform policies\n\nWould you like specific guidance on any of these areas?",
    "suggestions": [
      "Show me the app approval guidelines",
      "How do I write better app descriptions?",
      "What are common rejection reasons?"
    ]
  }
}
```

### Get AI Status
Retrieves AI service status and capabilities.

**Endpoint:** `GET /api/ai/chat`

**Authentication:** Required (Session)

**Response:**
```json
{
  "success": true,
  "status": {
    "configured": true,
    "available": true,
    "model": "gemini-2.0-flash-exp"
  },
  "context": {
    "platformStats": {
      "totalUsers": 1250,
      "totalApps": 450,
      "pendingApps": 23,
      "approvedApps": 380
    },
    "recentActivityCount": 10,
    "tablesCount": 8
  },
  "capabilities": [
    "App management assistance",
    "Platform statistics and insights",
    "Development recommendations",
    "Troubleshooting support",
    "Best practices guidance",
    "Approval process help"
  ]
}
```

### External AI API
Public API endpoint for external website integration.

**Endpoint:** `POST /api/ai/external`

**Authentication:** Required (API Key)

**Headers:**
```
X-API-Key: your-avena-ai-api-key
Content-Type: application/json
```

**Request Body:**
```json
{
  "message": "How do I submit an app to AveHub?",
  "website": "example.com",
  "context": {}
}
```

**Response:**
```json
{
  "success": true,
  "response": {
    "message": "To submit an app to AveHub, you need to:\n\n1. Create a developer account\n2. Prepare your app files and documentation\n3. Submit through the developer portal\n4. Wait for review (typically 5 business days)\n\nThe platform supports various file formats including mobile apps, desktop applications, and web apps.",
    "suggestions": [
      "What file formats are supported?",
      "How long does the review process take?"
    ],
    "timestamp": "2024-01-01T00:00:00.000Z"
  },
  "source": "AveHub Avena AI"
}
```

**Rate Limiting:** 100 requests per hour per API key

---

## Admin & Cleanup API

### Get Cleanup Statistics
Retrieves system cleanup statistics and pending app information.

**Endpoint:** `GET /api/admin/cleanup`

**Authentication:** Required (Session + Admin)

**Response:**
```json
{
  "success": true,
  "stats": {
    "pendingAppsCount": 23,
    "appsNearingExpiry": 5,
    "expiredAppsCount": 2,
    "totalStorageSize": **********
  }
}
```

### Perform Cleanup Operations
Executes various cleanup operations on the system.

**Endpoint:** `POST /api/admin/cleanup`

**Authentication:** Required (Session + Admin)

**Request Body:**
```json
{
  "operation": "expired-apps"
}
```

**Available Operations:**
- `expired-apps`: Clean up apps pending for more than 5 days
- `orphaned-files`: Remove files without corresponding database entries
- `send-warnings`: Send expiry warnings to developers
- `full-cleanup`: Perform all cleanup operations

**Response:**
```json
{
  "success": true,
  "operation": "expired-apps",
  "result": {
    "deletedApps": 3,
    "deletedFiles": 15,
    "errors": [],
    "warnings": [
      "Some files could not be deleted due to permissions"
    ],
    "warningsSent": 5
  }
}
```

---

## User Profile API

### Get User Profile
Retrieves the authenticated user's profile information.

**Endpoint:** `GET /api/user/profile`

**Authentication:** Required (Session)

**Response:**
```json
{
  "id": "user_123456789",
  "name": "John Developer",
  "email": "<EMAIL>",
  "image": "https://avatar.url/john.jpg",
  "admin": false,
  "isDeveloper": true,
  "createdAt": "2024-01-01T00:00:00.000Z",
  "updatedAt": "2024-01-01T00:00:00.000Z"
}
```

### Update User Profile
Updates the authenticated user's profile information.

**Endpoint:** `PUT /api/user/profile`

**Authentication:** Required (Session)

**Request Body:**
```json
{
  "name": "John Developer Updated",
  "email": "<EMAIL>"
}
```

**Response:**
```json
{
  "success": true,
  "user": {
    "id": "user_123456789",
    "name": "John Developer Updated",
    "email": "<EMAIL>",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  },
  "message": "Profile updated successfully"
}
```

---

## Error Handling

### Standard Error Response Format
```json
{
  "error": "Error message",
  "details": "Additional error details or array of validation errors",
  "code": "ERROR_CODE"
}
```

### Common HTTP Status Codes
- `200` - Success
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (authentication required)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `429` - Too Many Requests (rate limited)
- `500` - Internal Server Error

### Error Codes
- `VALIDATION_ERROR` - Request validation failed
- `AUTH_REQUIRED` - Authentication required
- `INSUFFICIENT_PERMISSIONS` - User lacks required permissions
- `RESOURCE_NOT_FOUND` - Requested resource not found
- `FILE_TOO_LARGE` - Uploaded file exceeds size limit
- `UNSUPPORTED_FILE_TYPE` - File type not supported
- `VIRUS_DETECTED` - File failed security scan
- `RATE_LIMIT_EXCEEDED` - Too many requests
- `AI_SERVICE_UNAVAILABLE` - AI service is not available
- `EMAIL_SERVICE_ERROR` - Email sending failed

---

## Rate Limiting

### API Rate Limits
- **General API**: 1000 requests per hour per user
- **File Upload**: 50 uploads per hour per user
- **AI Chat**: 100 messages per hour per user
- **External AI API**: 100 requests per hour per API key

### Rate Limit Headers
```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
```

---

## Email Notifications API

### Email Templates
The system supports the following email notification types:

- `APP_CREATED` - Sent when an app is submitted
- `APP_APPROVED` - Sent when an app is approved
- `APP_REJECTED` - Sent when an app is rejected
- `APP_SUSPENDED` - Sent when an app is suspended
- `PENDING_EXPIRY_WARNING` - Sent 1 day before app expires
- `PENDING_EXPIRED` - Sent when pending app is deleted

### Email Configuration
Configure email settings in environment variables:

```bash
# Zoho Email Configuration
ZOHO_SMTP_HOST="smtp.zoho.com"
ZOHO_SMTP_PORT="587"
ZOHO_SMTP_SECURE="false"
ZOHO_EMAIL_USER="<EMAIL>"
ZOHO_EMAIL_PASS="your-email-password"
ZOHO_EMAIL_FROM="<EMAIL>"

# Email Settings
SEND_EMAILS="true"
EMAIL_RATE_LIMIT="100"
```

### User Email Preferences
Users can control their email notification preferences:

```json
{
  "emailNotifications": true,
  "notifyAppUpdates": true,
  "notifyAppApproval": true,
  "notifyAppRejection": true,
  "notifyAppSuspended": true
}
```

---

## File Security & Virus Scanning

### Supported Virus Scanners

#### VirusTotal Integration
Configure VirusTotal API for comprehensive virus scanning:

```bash
VIRUSTOTAL_API_KEY="your-virustotal-api-key"
```

**Features:**
- Multi-engine scanning (60+ antivirus engines)
- Real-time threat detection
- Detailed scan reports
- URL scanning support

#### ClamAV Integration (Optional)
For local virus scanning:

```bash
CLAMAV_HOST="localhost"
CLAMAV_PORT="3310"
```

**Features:**
- Local scanning (no external API calls)
- Fast scanning for large files
- Regular signature updates
- Open source solution

### File Validation Process

1. **File Type Detection**: Uses magic numbers (file signatures)
2. **Size Validation**: Enforces category-specific size limits
3. **Name Validation**: Checks for suspicious patterns
4. **Virus Scanning**: Scans with available engines
5. **Quarantine**: Infected files are quarantined and deleted

---

## Cleanup & Maintenance

### Automated Cleanup
The system automatically manages pending apps and storage:

- **Pending App Expiry**: Apps pending for 5+ days are deleted
- **Warning Notifications**: Users receive warnings 1 day before expiry
- **Orphaned Files**: Files without database entries are removed
- **Storage Optimization**: Unused storage is reclaimed

### Manual Cleanup Operations
Administrators can trigger cleanup operations:

```bash
# Run cleanup script manually
npx tsx src/scripts/cleanup-cron.ts
```

### Cron Job Setup
Add to system crontab for daily cleanup:

```bash
# Daily cleanup at 2 AM
0 2 * * * /usr/bin/node /path/to/app/src/scripts/cleanup-cron.ts
```

---

## Development & Testing

### Environment Setup
1. Clone the repository
2. Install dependencies: `pnpm install`
3. Configure environment variables in `.env`
4. Run database migrations: `pnpm db:push`
5. Seed email templates: `npx tsx src/scripts/seed-email-templates.ts`
6. Start development server: `pnpm dev`

### Testing API Endpoints
Use tools like Postman, curl, or the built-in API testing interface:

```bash
# Test AI chat endpoint
curl -X POST http://localhost:3000/api/ai/chat \
  -H "Content-Type: application/json" \
  -H "Cookie: next-auth.session-token=your-session-token" \
  -d '{"messages":[{"role":"user","content":"Hello","timestamp":"2024-01-01T00:00:00.000Z"}]}'
```

### API Testing Interface
Access the interactive API documentation at:
```
http://localhost:3000/api-docs
```

---

## Support & Resources

### Documentation
- **API Reference**: Complete endpoint documentation
- **Integration Guides**: Step-by-step integration tutorials
- **Best Practices**: Recommended implementation patterns
- **Troubleshooting**: Common issues and solutions

### Support Channels
- **Email**: <EMAIL>
- **Documentation**: [https://docs.avehub.com](https://docs.avehub.com)
- **Developer Forum**: [https://forum.avehub.com](https://forum.avehub.com)
- **Status Page**: [https://status.avehub.com](https://status.avehub.com)

### Service Level Agreement
- **Uptime**: 99.9% availability guarantee
- **Response Time**: < 200ms for most endpoints
- **Support Response**: 24 hours for technical issues
- **Maintenance Windows**: Announced 48 hours in advance

---

## Changelog

### Version 2.0.0 (Latest)
- ✅ Enhanced file upload with virus scanning
- ✅ Avena AI integration with Google Gemini 2.0 Flash
- ✅ Comprehensive email notification system
- ✅ Automated cleanup and maintenance
- ✅ External AI API for website integration
- ✅ Advanced file type validation
- ✅ Admin cleanup operations

### Version 1.0.0
- ✅ Basic app management API
- ✅ User authentication and profiles
- ✅ File upload functionality
- ✅ Admin panel features
