export interface CloudStorageConfig {
  endpoint: string
  region: string
  accessKeyId: string
  secretAccessKey: string
  bucket: string
  publicUrl: string
}

export interface UploadResult {
  success: boolean
  url?: string
  key?: string
  error?: string
}

export interface MultipartUpload {
  uploadId: string
  key: string
  parts: Array<{
    ETag: string
    PartNumber: number
  }>
}

/**
 * Cloud storage service using Backblaze B2 (S3-compatible, 10GB free)
 */
export class CloudStorageService {
  private s3Client: S3Client
  private config: CloudStorageConfig

  constructor() {
    this.config = {
      endpoint: process.env.R2_ENDPOINT || '',
      region: process.env.R2_REGION || 'us-west-004',
      accessKeyId: process.env.R2_ACCESS_KEY_ID || '',
      secretAccessKey: process.env.R2_SECRET_ACCESS_KEY || '',
      bucket: process.env.R2_BUCKET_NAME || '',
      publicUrl: process.env.R2_PUBLIC_URL || ''
    }

    this.s3Client = new S3Client({
      endpoint: this.config.endpoint,
      region: this.config.region,
      credentials: {
        accessKeyId: this.config.accessKeyId,
        secretAccessKey: this.config.secretAccessKey
      },
      // Backblaze B2 specific configuration
      forcePathStyle: true,
      maxAttempts: 3
    })
  }

  /**
   * Check if cloud storage is properly configured
   */
  isConfigured(): boolean {
    return !!(
      this.config.endpoint &&
      this.config.accessKeyId &&
      this.config.secretAccessKey &&
      this.config.bucket
    )
  }

  /**
   * Upload a file directly to cloud storage
   */
  async uploadFile(
    buffer: Buffer,
    key: string,
    contentType: string,
    metadata?: Record<string, string>
  ): Promise<UploadResult> {
    try {
      console.log(`[BACKBLAZE B2] Uploading file: ${key} (${buffer.length} bytes)`)

      const command = new PutObjectCommand({
        Bucket: this.config.bucket,
        Key: key,
        Body: buffer,
        ContentType: contentType,
        Metadata: metadata
      })

      await this.s3Client.send(command)

      const publicUrl = `${this.config.publicUrl}/${key}`

      console.log(`[BACKBLAZE B2] Upload successful: ${publicUrl}`)

      return {
        success: true,
        url: publicUrl,
        key
      }
    } catch (error) {
      console.error('[BACKBLAZE B2] Upload failed:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Upload failed'
      }
    }
  }

  /**
   * Start multipart upload for large files
   */
  async startMultipartUpload(
    key: string,
    contentType: string,
    metadata?: Record<string, string>
  ): Promise<{ uploadId: string; key: string } | null> {
    try {
      console.log(`[CLOUD STORAGE] Starting multipart upload: ${key}`)

      const command = new CreateMultipartUploadCommand({
        Bucket: this.config.bucket,
        Key: key,
        ContentType: contentType,
        Metadata: metadata
      })

      const response = await this.s3Client.send(command)

      if (!response.UploadId) {
        throw new Error('Failed to get upload ID')
      }

      console.log(`[CLOUD STORAGE] Multipart upload started: ${response.UploadId}`)

      return {
        uploadId: response.UploadId,
        key
      }
    } catch (error) {
      console.error('[CLOUD STORAGE] Failed to start multipart upload:', error)
      return null
    }
  }

  /**
   * Upload a part in multipart upload
   */
  async uploadPart(
    key: string,
    uploadId: string,
    partNumber: number,
    buffer: Buffer
  ): Promise<{ ETag: string; PartNumber: number } | null> {
    try {
      console.log(`[CLOUD STORAGE] Uploading part ${partNumber} for ${key}`)

      const command = new UploadPartCommand({
        Bucket: this.config.bucket,
        Key: key,
        UploadId: uploadId,
        PartNumber: partNumber,
        Body: buffer
      })

      const response = await this.s3Client.send(command)

      if (!response.ETag) {
        throw new Error('Failed to get ETag')
      }

      return {
        ETag: response.ETag,
        PartNumber: partNumber
      }
    } catch (error) {
      console.error(`[CLOUD STORAGE] Failed to upload part ${partNumber}:`, error)
      return null
    }
  }

  /**
   * Complete multipart upload
   */
  async completeMultipartUpload(
    key: string,
    uploadId: string,
    parts: Array<{ ETag: string; PartNumber: number }>
  ): Promise<UploadResult> {
    try {
      console.log(`[CLOUD STORAGE] Completing multipart upload: ${key}`)

      const command = new CompleteMultipartUploadCommand({
        Bucket: this.config.bucket,
        Key: key,
        UploadId: uploadId,
        MultipartUpload: {
          Parts: parts.sort((a, b) => a.PartNumber - b.PartNumber)
        }
      })

      await this.s3Client.send(command)

      const publicUrl = `${this.config.publicUrl}/${key}`

      console.log(`[CLOUD STORAGE] Multipart upload completed: ${publicUrl}`)

      return {
        success: true,
        url: publicUrl,
        key
      }
    } catch (error) {
      console.error('[CLOUD STORAGE] Failed to complete multipart upload:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to complete upload'
      }
    }
  }

  /**
   * Abort multipart upload
   */
  async abortMultipartUpload(key: string, uploadId: string): Promise<void> {
    try {
      console.log(`[CLOUD STORAGE] Aborting multipart upload: ${key}`)

      const command = new AbortMultipartUploadCommand({
        Bucket: this.config.bucket,
        Key: key,
        UploadId: uploadId
      })

      await this.s3Client.send(command)

      console.log(`[CLOUD STORAGE] Multipart upload aborted: ${key}`)
    } catch (error) {
      console.error('[CLOUD STORAGE] Failed to abort multipart upload:', error)
    }
  }

  /**
   * Delete a file from cloud storage
   */
  async deleteFile(key: string): Promise<boolean> {
    try {
      console.log(`[CLOUD STORAGE] Deleting file: ${key}`)

      const command = new DeleteObjectCommand({
        Bucket: this.config.bucket,
        Key: key
      })

      await this.s3Client.send(command)

      console.log(`[CLOUD STORAGE] File deleted: ${key}`)
      return true
    } catch (error) {
      console.error('[CLOUD STORAGE] Failed to delete file:', error)
      return false
    }
  }

  /**
   * Get a presigned URL for direct upload (for frontend)
   */
  async getPresignedUploadUrl(
    key: string,
    contentType: string,
    expiresIn: number = 3600
  ): Promise<string | null> {
    try {
      const command = new PutObjectCommand({
        Bucket: this.config.bucket,
        Key: key,
        ContentType: contentType
      })

      const presignedUrl = await getSignedUrl(this.s3Client, command, { expiresIn })
      return presignedUrl
    } catch (error) {
      console.error('[CLOUD STORAGE] Failed to generate presigned URL:', error)
      return null
    }
  }

  /**
   * Download a file from cloud storage
   */
  async downloadFile(key: string): Promise<Buffer | null> {
    try {
      console.log(`[CLOUD STORAGE] Downloading file: ${key}`)

      const command = new GetObjectCommand({
        Bucket: this.config.bucket,
        Key: key
      })

      const response = await this.s3Client.send(command)

      if (!response.Body) {
        throw new Error('No file content received')
      }

      const buffer = Buffer.from(await response.Body.transformToByteArray())

      console.log(`[CLOUD STORAGE] File downloaded: ${key} (${buffer.length} bytes)`)
      return buffer
    } catch (error) {
      console.error('[CLOUD STORAGE] Failed to download file:', error)
      return null
    }
  }

  /**
   * Generate a storage key for an app file
   */
  generateFileKey(appId: string, fileName: string, fileType: string): string {
    const timestamp = Date.now()
    const sanitizedFileName = fileName.replace(/[^a-zA-Z0-9.-]/g, '_')
    return `apps/${appId}/${timestamp}_${sanitizedFileName}`
  }
}

// Export singleton instance
export const cloudStorageService = new CloudStorageService()
