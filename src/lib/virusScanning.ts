import { SecurityScanResult } from '@/types/fileTypes'

/**
 * Virus scanning service interface
 */
export interface VirusScanService {
  name: string
  scanFile(file: File | Buffer, filename: string): Promise<SecurityScanResult>
  scanUrl(url: string): Promise<SecurityScanResult>
  isAvailable(): Promise<boolean>
}

/**
 * VirusTotal API integration
 * Requires VIRUSTOTAL_API_KEY environment variable
 */
export class VirusTotalScanner implements VirusScanService {
  name = 'VirusTotal'
  private apiKey: string
  private baseUrl = 'https://www.virustotal.com/vtapi/v2'

  constructor() {
    this.apiKey = process.env.VIRUSTOTAL_API_KEY || ''
  }

  async isAvailable(): Promise<boolean> {
    return !!this.apiKey
  }

  async scanFile(file: File | Buffer, filename: string): Promise<SecurityScanResult> {
    if (!this.apiKey) {
      throw new Error('VirusTotal API key not configured')
    }

    try {
      // Convert File to Buffer if needed
      const buffer = file instanceof File ? Buffer.from(await file.arrayBuffer()) : file

      // Upload file for scanning
      const formData = new FormData()
      formData.append('apikey', this.apiKey)
      formData.append('file', new Blob([buffer]), filename)

      const uploadResponse = await fetch(`${this.baseUrl}/file/scan`, {
        method: 'POST',
        body: formData
      })

      if (!uploadResponse.ok) {
        throw new Error(`Upload failed: ${uploadResponse.statusText}`)
      }

      const uploadResult = await uploadResponse.json()
      const scanId = uploadResult.scan_id

      // Wait for scan to complete and get results
      return await this.getScanResults(scanId)

    } catch (error) {
      return {
        isClean: false,
        scanEngine: this.name,
        scanDate: new Date(),
        threats: [`Scan error: ${error instanceof Error ? error.message : 'Unknown error'}`]
      }
    }
  }

  async scanUrl(url: string): Promise<SecurityScanResult> {
    if (!this.apiKey) {
      throw new Error('VirusTotal API key not configured')
    }

    try {
      const formData = new FormData()
      formData.append('apikey', this.apiKey)
      formData.append('url', url)

      const response = await fetch(`${this.baseUrl}/url/scan`, {
        method: 'POST',
        body: formData
      })

      if (!response.ok) {
        throw new Error(`URL scan failed: ${response.statusText}`)
      }

      const result = await response.json()
      return await this.getScanResults(result.scan_id)

    } catch (error) {
      return {
        isClean: false,
        scanEngine: this.name,
        scanDate: new Date(),
        threats: [`URL scan error: ${error instanceof Error ? error.message : 'Unknown error'}`]
      }
    }
  }

  private async getScanResults(scanId: string, maxRetries = 10): Promise<SecurityScanResult> {
    for (let i = 0; i < maxRetries; i++) {
      try {
        const response = await fetch(
          `${this.baseUrl}/file/report?apikey=${this.apiKey}&resource=${scanId}`
        )

        if (!response.ok) {
          throw new Error(`Report fetch failed: ${response.statusText}`)
        }

        const result = await response.json()

        if (result.response_code === 1) {
          // Scan completed
          const threats: string[] = []
          
          if (result.scans) {
            Object.entries(result.scans).forEach(([engine, scan]: [string, any]) => {
              if (scan.detected) {
                threats.push(`${engine}: ${scan.result}`)
              }
            })
          }

          return {
            isClean: result.positives === 0,
            scanEngine: this.name,
            scanDate: new Date(result.scan_date),
            threats,
            scanId,
            details: result
          }
        } else if (result.response_code === -2) {
          // Still scanning, wait and retry
          await new Promise(resolve => setTimeout(resolve, 5000))
          continue
        } else {
          throw new Error(`Scan failed with response code: ${result.response_code}`)
        }
      } catch (error) {
        if (i === maxRetries - 1) {
          return {
            isClean: false,
            scanEngine: this.name,
            scanDate: new Date(),
            threats: [`Scan result error: ${error instanceof Error ? error.message : 'Unknown error'}`]
          }
        }
        await new Promise(resolve => setTimeout(resolve, 2000))
      }
    }

    return {
      isClean: false,
      scanEngine: this.name,
      scanDate: new Date(),
      threats: ['Scan timeout - could not retrieve results']
    }
  }
}

/**
 * ClamAV scanner (for local scanning)
 * Requires ClamAV to be installed and clamd running
 */
export class ClamAVScanner implements VirusScanService {
  name = 'ClamAV'
  private clamHost: string
  private clamPort: number

  constructor() {
    this.clamHost = process.env.CLAMAV_HOST || 'localhost'
    this.clamPort = parseInt(process.env.CLAMAV_PORT || '3310')
  }

  async isAvailable(): Promise<boolean> {
    try {
      // Try to connect to ClamAV daemon
      const net = await import('net')
      return new Promise((resolve) => {
        const socket = net.createConnection(this.clamPort, this.clamHost)
        socket.on('connect', () => {
          socket.end()
          resolve(true)
        })
        socket.on('error', () => {
          resolve(false)
        })
        setTimeout(() => {
          socket.destroy()
          resolve(false)
        }, 5000)
      })
    } catch {
      return false
    }
  }

  async scanFile(file: File | Buffer, filename: string): Promise<SecurityScanResult> {
    // ClamAV implementation would go here
    // This is a placeholder for local ClamAV integration
    return {
      isClean: true,
      scanEngine: this.name,
      scanDate: new Date(),
      threats: []
    }
  }

  async scanUrl(url: string): Promise<SecurityScanResult> {
    // ClamAV doesn't support URL scanning directly
    throw new Error('URL scanning not supported by ClamAV')
  }
}

/**
 * Main virus scanning service
 */
export class VirusScanningService {
  private scanners: VirusScanService[]

  constructor() {
    this.scanners = [
      new VirusTotalScanner(),
      new ClamAVScanner()
    ]
  }

  async scanFile(file: File | Buffer, filename: string): Promise<SecurityScanResult> {
    // Try scanners in order of preference
    for (const scanner of this.scanners) {
      if (await scanner.isAvailable()) {
        try {
          return await scanner.scanFile(file, filename)
        } catch (error) {
          console.error(`Scanner ${scanner.name} failed:`, error)
          continue
        }
      }
    }

    // If no scanners are available, return a warning result
    return {
      isClean: true, // Assume clean if no scanners available
      scanEngine: 'None',
      scanDate: new Date(),
      threats: [],
      scanId: 'no-scan-available'
    }
  }

  async getAvailableScanners(): Promise<string[]> {
    const available: string[] = []
    for (const scanner of this.scanners) {
      if (await scanner.isAvailable()) {
        available.push(scanner.name)
      }
    }
    return available
  }
}

// Export singleton instance
export const virusScanningService = new VirusScanningService()
