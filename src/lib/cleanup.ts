import { prisma } from './prisma'
import { appNotificationService } from './email'
import { unlink, rmdir, access } from 'fs/promises'
import { join } from 'path'

export interface CleanupResult {
  deletedApps: number
  deletedFiles: number
  errors: string[]
  warnings: string[]
}

/**
 * Cleanup service for managing pending apps and expired files
 */
export class CleanupService {
  private readonly PENDING_EXPIRY_DAYS = 5
  private readonly WARNING_DAYS_BEFORE_EXPIRY = 1

  /**
   * Clean up expired pending apps and their files
   */
  async cleanupExpiredPendingApps(): Promise<CleanupResult> {
    const result: CleanupResult = {
      deletedApps: 0,
      deletedFiles: 0,
      errors: [],
      warnings: []
    }

    try {
      // Find apps that have expired (pending for more than 5 days)
      const expiredApps = await prisma.app.findMany({
        where: {
          status: 'PENDING',
          createdAt: {
            lt: new Date(Date.now() - this.PENDING_EXPIRY_DAYS * 24 * 60 * 60 * 1000)
          }
        },
        include: {
          developer: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      })

      console.log(`Found ${expiredApps.length} expired pending apps`)

      for (const app of expiredApps) {
        try {
          // Notify user about app deletion
          if (app.developer.email) {
            await appNotificationService.sendTemplatedEmail(
              'PENDING_EXPIRED',
              app.developer.email,
              {
                appName: app.name,
                userName: app.developer.name || 'User',
                expiryReason: 'App was pending approval for more than 5 days',
                dashboardUrl: `${process.env.NEXTAUTH_URL}/apps`
              },
              app.developer.id,
              app.id
            )
          }

          // Delete associated files
          const filesDeleted = await this.deleteAppFiles(app.name, app.tempStoragePath)
          result.deletedFiles += filesDeleted

          // Delete app from database
          await prisma.app.delete({
            where: { id: app.id }
          })

          result.deletedApps++
          console.log(`Deleted expired app: ${app.name} (ID: ${app.id})`)

        } catch (error) {
          const errorMsg = `Failed to delete app ${app.name}: ${error instanceof Error ? error.message : 'Unknown error'}`
          result.errors.push(errorMsg)
          console.error(errorMsg)
        }
      }

      return result

    } catch (error) {
      const errorMsg = `Cleanup process failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      result.errors.push(errorMsg)
      console.error(errorMsg)
      return result
    }
  }

  /**
   * Send warning notifications for apps nearing expiry
   */
  async sendExpiryWarnings(): Promise<number> {
    try {
      const warningDate = new Date(Date.now() + this.WARNING_DAYS_BEFORE_EXPIRY * 24 * 60 * 60 * 1000)
      
      const appsNearingExpiry = await prisma.app.findMany({
        where: {
          status: 'PENDING',
          createdAt: {
            lt: new Date(Date.now() - (this.PENDING_EXPIRY_DAYS - this.WARNING_DAYS_BEFORE_EXPIRY) * 24 * 60 * 60 * 1000),
            gte: new Date(Date.now() - this.PENDING_EXPIRY_DAYS * 24 * 60 * 60 * 1000)
          }
        },
        include: {
          developer: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      })

      let warningsSent = 0

      for (const app of appsNearingExpiry) {
        try {
          if (app.developer.email) {
            const expiryDate = new Date(app.createdAt.getTime() + this.PENDING_EXPIRY_DAYS * 24 * 60 * 60 * 1000)
            
            await appNotificationService.notifyPendingExpiry(
              app.developer.id,
              app.id,
              app.name,
              app.developer.email,
              expiryDate
            )
            
            warningsSent++
          }
        } catch (error) {
          console.error(`Failed to send expiry warning for app ${app.name}:`, error)
        }
      }

      return warningsSent

    } catch (error) {
      console.error('Failed to send expiry warnings:', error)
      return 0
    }
  }

  /**
   * Delete app files from storage
   */
  private async deleteAppFiles(appName: string, tempStoragePath?: string | null): Promise<number> {
    let deletedCount = 0

    try {
      // Delete from main storage
      const appSlug = appName.toLowerCase().replace(/\s+/g, '-')
      const mainStoragePath = join(process.cwd(), 'public', 'storage', 'files', appSlug)
      
      if (await this.pathExists(mainStoragePath)) {
        await this.deleteDirectory(mainStoragePath)
        deletedCount++
      }

      // Delete from temporary storage if specified
      if (tempStoragePath && await this.pathExists(tempStoragePath)) {
        await this.deleteDirectory(tempStoragePath)
        deletedCount++
      }

    } catch (error) {
      console.error(`Error deleting files for app ${appName}:`, error)
    }

    return deletedCount
  }

  /**
   * Check if path exists
   */
  private async pathExists(path: string): Promise<boolean> {
    try {
      await access(path)
      return true
    } catch {
      return false
    }
  }

  /**
   * Recursively delete directory and its contents
   */
  private async deleteDirectory(dirPath: string): Promise<void> {
    try {
      const fs = await import('fs/promises')
      const entries = await fs.readdir(dirPath, { withFileTypes: true })

      for (const entry of entries) {
        const fullPath = join(dirPath, entry.name)
        
        if (entry.isDirectory()) {
          await this.deleteDirectory(fullPath)
        } else {
          await unlink(fullPath)
        }
      }

      await rmdir(dirPath)
    } catch (error) {
      console.error(`Error deleting directory ${dirPath}:`, error)
      throw error
    }
  }

  /**
   * Clean up orphaned files (files without corresponding database entries)
   */
  async cleanupOrphanedFiles(): Promise<CleanupResult> {
    const result: CleanupResult = {
      deletedApps: 0,
      deletedFiles: 0,
      errors: [],
      warnings: []
    }

    try {
      const fs = await import('fs/promises')
      const storageDir = join(process.cwd(), 'public', 'storage', 'files')

      if (!await this.pathExists(storageDir)) {
        return result
      }

      const entries = await fs.readdir(storageDir, { withFileTypes: true })
      
      for (const entry of entries) {
        if (entry.isDirectory()) {
          const appSlug = entry.name
          
          // Check if there's a corresponding app in the database
          const app = await prisma.app.findFirst({
            where: {
              name: {
                equals: appSlug.replace(/-/g, ' '),
                mode: 'insensitive'
              }
            }
          })

          if (!app) {
            // No corresponding app found, delete the directory
            try {
              const dirPath = join(storageDir, appSlug)
              await this.deleteDirectory(dirPath)
              result.deletedFiles++
              console.log(`Deleted orphaned directory: ${appSlug}`)
            } catch (error) {
              result.errors.push(`Failed to delete orphaned directory ${appSlug}: ${error}`)
            }
          }
        }
      }

    } catch (error) {
      result.errors.push(`Orphaned files cleanup failed: ${error}`)
    }

    return result
  }

  /**
   * Get cleanup statistics
   */
  async getCleanupStats(): Promise<{
    pendingAppsCount: number
    appsNearingExpiry: number
    expiredAppsCount: number
    totalStorageSize: number
  }> {
    try {
      const now = new Date()
      const expiryThreshold = new Date(now.getTime() - this.PENDING_EXPIRY_DAYS * 24 * 60 * 60 * 1000)
      const warningThreshold = new Date(now.getTime() - (this.PENDING_EXPIRY_DAYS - this.WARNING_DAYS_BEFORE_EXPIRY) * 24 * 60 * 60 * 1000)

      const [pendingAppsCount, appsNearingExpiry, expiredAppsCount] = await Promise.all([
        prisma.app.count({
          where: { status: 'PENDING' }
        }),
        prisma.app.count({
          where: {
            status: 'PENDING',
            createdAt: {
              lt: warningThreshold,
              gte: expiryThreshold
            }
          }
        }),
        prisma.app.count({
          where: {
            status: 'PENDING',
            createdAt: { lt: expiryThreshold }
          }
        })
      ])

      // Calculate total storage size (simplified)
      const totalStorageSize = await this.calculateStorageSize()

      return {
        pendingAppsCount,
        appsNearingExpiry,
        expiredAppsCount,
        totalStorageSize
      }

    } catch (error) {
      console.error('Failed to get cleanup stats:', error)
      return {
        pendingAppsCount: 0,
        appsNearingExpiry: 0,
        expiredAppsCount: 0,
        totalStorageSize: 0
      }
    }
  }

  /**
   * Calculate total storage size used by apps
   */
  private async calculateStorageSize(): Promise<number> {
    try {
      const apps = await prisma.app.findMany({
        select: { fileSize: true }
      })

      return apps.reduce((total, app) => total + (app.fileSize || 0), 0)
    } catch {
      return 0
    }
  }
}

// Export singleton instance
export const cleanupService = new CleanupService()
