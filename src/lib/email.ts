import { EmailType, EmailStatus } from '@prisma/client'
import { prisma } from './prisma'

export interface EmailConfig {
  host: string
  port: number
  secure: boolean
  auth: {
    user: string
    pass: string
  }
  from: string
}

export interface EmailData {
  to: string
  subject: string
  html: string
  text?: string
  attachments?: Array<{
    filename: string
    content: Buffer | string
    contentType?: string
  }>
}

export interface EmailTemplateData {
  [key: string]: string | number | boolean | Date
}

/**
 * Email service using Zoho Mail SMTP
 */
export class EmailService {
  private config: EmailConfig

  constructor() {
    this.config = {
      host: process.env.ZOHO_SMTP_HOST || 'smtp.zoho.com',
      port: parseInt(process.env.ZOHO_SMTP_PORT || '587'),
      secure: process.env.ZOHO_SMTP_SECURE === 'true',
      auth: {
        user: process.env.ZOHO_EMAIL_USER || '',
        pass: process.env.ZOHO_EMAIL_PASS || ''
      },
      from: process.env.ZOHO_EMAIL_FROM || process.env.ZOHO_EMAIL_USER || ''
    }
  }

  /**
   * Send email using Zoho SMTP
   */
  async sendEmail(emailData: EmailData): Promise<boolean> {
    const startTime = Date.now()
    console.log(`[EMAIL] Starting email send process for: ${emailData.subject}`)

    try {
      // Check if email sending is enabled
      if (process.env.SEND_EMAILS !== 'true') {
        console.log('[EMAIL] Email sending disabled via SEND_EMAILS environment variable')
        return true
      }

      // Validate configuration
      if (!this.isConfigured()) {
        console.error('[EMAIL] Email service not properly configured')
        console.error('[EMAIL] Missing configuration:', {
          hasUser: !!this.config.auth.user,
          hasPass: !!this.config.auth.pass,
          hasFrom: !!this.config.from,
          host: this.config.host,
          port: this.config.port
        })
        return false
      }

      console.log('[EMAIL] Email configuration:', {
        host: this.config.host,
        port: this.config.port,
        secure: this.config.secure,
        from: this.config.from,
        to: emailData.to
      })

      // Import nodemailer dynamically
      const nodemailer = await import('nodemailer')

      console.log('[EMAIL] Creating SMTP transporter...')
      const transporter = nodemailer.default.createTransport({
        host: this.config.host,
        port: this.config.port,
        secure: this.config.secure,
        auth: {
          user: this.config.auth.user,
          pass: this.config.auth.pass
        },
        debug: process.env.NODE_ENV === 'development',
        logger: process.env.NODE_ENV === 'development'
      })

      // Verify SMTP connection
      console.log('[EMAIL] Verifying SMTP connection...')
      try {
        await transporter.verify()
        console.log('[EMAIL] SMTP connection verified successfully')
      } catch (verifyError) {
        console.error('[EMAIL] SMTP connection verification failed:', verifyError)
        return false
      }

      console.log('[EMAIL] Sending email...')
      const result = await transporter.sendMail({
        from: this.config.from,
        to: emailData.to,
        subject: emailData.subject,
        html: emailData.html,
        text: emailData.text,
        attachments: emailData.attachments
      })

      const duration = Date.now() - startTime
      console.log(`[EMAIL] Email sent successfully in ${duration}ms:`, {
        to: emailData.to,
        subject: emailData.subject,
        messageId: result.messageId,
        response: result.response
      })

      return !!result.messageId
    } catch (error) {
      const duration = Date.now() - startTime
      console.error(`[EMAIL] Email sending failed after ${duration}ms:`, {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        to: emailData.to,
        subject: emailData.subject
      })
      return false
    }
  }

  /**
   * Send templated email
   */
  async sendTemplatedEmail(
    templateName: string,
    to: string,
    templateData: EmailTemplateData,
    userId: string,
    appId?: string
  ): Promise<boolean> {
    console.log(`[EMAIL] Starting templated email process:`, {
      templateName,
      to,
      userId,
      appId,
      dataKeys: Object.keys(templateData)
    })

    try {
      // Get email template
      console.log(`[EMAIL] Fetching template: ${templateName}`)
      const template = await prisma.emailTemplate.findFirst({
        where: {
          name: templateName,
          isActive: true
        }
      })

      if (!template) {
        console.error(`[EMAIL] Template not found: ${templateName}`)
        throw new Error(`Email template '${templateName}' not found`)
      }

      console.log(`[EMAIL] Template found:`, {
        id: template.id,
        name: template.name,
        variables: template.variables
      })

      // Process template variables
      console.log(`[EMAIL] Processing template variables...`)
      const subject = this.processTemplate(template.subject, templateData)
      const html = this.processTemplate(template.htmlContent, templateData)
      const text = template.textContent ? this.processTemplate(template.textContent, templateData) : undefined

      console.log(`[EMAIL] Template processed:`, {
        subject,
        hasHtml: !!html,
        hasText: !!text,
        htmlLength: html.length
      })

      // Create email log entry
      console.log(`[EMAIL] Creating email log entry...`)
      const emailLog = await prisma.emailLog.create({
        data: {
          userId,
          appId,
          emailType: templateName as EmailType,
          recipient: to,
          subject,
          content: html,
          status: EmailStatus.PENDING
        }
      })

      console.log(`[EMAIL] Email log created with ID: ${emailLog.id}`)

      // Send email
      console.log(`[EMAIL] Sending templated email...`)
      const success = await this.sendEmail({
        to,
        subject,
        html,
        text
      })

      // Update email log
      console.log(`[EMAIL] Updating email log with result: ${success ? 'SUCCESS' : 'FAILED'}`)
      await prisma.emailLog.update({
        where: { id: emailLog.id },
        data: {
          status: success ? EmailStatus.SENT : EmailStatus.FAILED,
          sentAt: success ? new Date() : undefined,
          errorMessage: success ? undefined : 'Failed to send email'
        }
      })

      console.log(`[EMAIL] Templated email process completed:`, {
        templateName,
        success,
        logId: emailLog.id
      })

      return success
    } catch (error) {
      console.error(`[EMAIL] Templated email sending failed:`, {
        templateName,
        to,
        userId,
        appId,
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      })
      return false
    }
  }

  /**
   * Process template with variables
   */
  private processTemplate(template: string, data: EmailTemplateData): string {
    let processed = template

    Object.entries(data).forEach(([key, value]) => {
      const placeholder = `{{${key}}}`
      const stringValue = this.formatTemplateValue(value)
      processed = processed.replace(new RegExp(placeholder, 'g'), stringValue)
    })

    return processed
  }

  /**
   * Format template value based on type
   */
  private formatTemplateValue(value: string | number | boolean | Date): string {
    if (value instanceof Date) {
      return value.toLocaleDateString()
    }
    if (typeof value === 'boolean') {
      return value ? 'Yes' : 'No'
    }
    return String(value)
  }

  /**
   * Check if email service is configured
   */
  isConfigured(): boolean {
    return !!(this.config.auth.user && this.config.auth.pass && this.config.from)
  }
}

/**
 * Email notification service for app lifecycle events
 */
export class AppNotificationService {
  private emailService: EmailService

  constructor() {
    this.emailService = new EmailService()
  }

  async notifyAppCreated(userId: string, appId: string, appName: string, userEmail: string): Promise<void> {
    console.log(`[NOTIFICATION] Starting APP_CREATED notification:`, {
      userId,
      appId,
      appName,
      userEmail
    })

    if (!this.emailService.isConfigured()) {
      console.log('[NOTIFICATION] Email service not configured, skipping notification')
      return
    }

    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { emailNotifications: true, notifyAppUpdates: true }
    })

    console.log(`[NOTIFICATION] User email preferences:`, {
      userId,
      emailNotifications: user?.emailNotifications,
      notifyAppUpdates: user?.notifyAppUpdates
    })

    if (!user?.emailNotifications || !user?.notifyAppUpdates) {
      console.log('[NOTIFICATION] User has disabled email notifications, skipping')
      return
    }

    const success = await this.emailService.sendTemplatedEmail(
      'APP_CREATED',
      userEmail,
      {
        appName,
        userName: await this.getUserName(userId),
        dashboardUrl: `${process.env.NEXTAUTH_URL}/apps`
      },
      userId,
      appId
    )

    console.log(`[NOTIFICATION] APP_CREATED notification completed:`, {
      userId,
      appId,
      success
    })
  }

  async notifyAppApproved(userId: string, appId: string, appName: string, userEmail: string): Promise<void> {
    if (!this.emailService.isConfigured()) return

    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { emailNotifications: true, notifyAppApproval: true }
    })

    if (!user?.emailNotifications || !user?.notifyAppApproval) return

    await this.emailService.sendTemplatedEmail(
      'APP_APPROVED',
      userEmail,
      {
        appName,
        userName: await this.getUserName(userId),
        appUrl: `${process.env.NEXTAUTH_URL}/apps/${appId}`
      },
      userId,
      appId
    )
  }

  async notifyAppRejected(userId: string, appId: string, appName: string, userEmail: string, reason?: string): Promise<void> {
    if (!this.emailService.isConfigured()) return

    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { emailNotifications: true, notifyAppRejection: true }
    })

    if (!user?.emailNotifications || !user?.notifyAppRejection) return

    await this.emailService.sendTemplatedEmail(
      'APP_REJECTED',
      userEmail,
      {
        appName,
        userName: await this.getUserName(userId),
        reason: reason || 'No specific reason provided',
        dashboardUrl: `${process.env.NEXTAUTH_URL}/apps`
      },
      userId,
      appId
    )
  }

  async notifyPendingExpiry(userId: string, appId: string, appName: string, userEmail: string, expiryDate: Date): Promise<void> {
    if (!this.emailService.isConfigured()) return

    await this.emailService.sendTemplatedEmail(
      'PENDING_EXPIRY_WARNING',
      userEmail,
      {
        appName,
        userName: await this.getUserName(userId),
        expiryDate: expiryDate.toLocaleDateString(),
        dashboardUrl: `${process.env.NEXTAUTH_URL}/apps`
      },
      userId,
      appId
    )
  }

  private async getUserName(userId: string): Promise<string> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { name: true }
    })
    return user?.name || 'User'
  }
}

// Export singleton instances
export const emailService = new EmailService()
export const appNotificationService = new AppNotificationService()
