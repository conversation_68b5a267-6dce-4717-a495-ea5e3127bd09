import { useState, useCallback, useRef } from 'react'

export interface UploadProgress {
  progress: number // 0-100
  uploadedBytes: number
  totalBytes: number
  speed: number // bytes per second
  timeRemaining: number // seconds
  currentChunk: number
  totalChunks: number
}

export interface UploadResult {
  success: boolean
  downloadUrl?: string
  fileSize?: number
  fileType?: string
  error?: string
  details?: string[]
}

export interface UseChunkedUploadOptions {
  chunkSize?: number // Default 1MB
  maxRetries?: number // Default 3
  onProgress?: (progress: UploadProgress) => void
  onComplete?: (result: UploadResult) => void
  onError?: (error: string) => void
}

export function useChunkedUpload(appId: string, options: UseChunkedUploadOptions = {}) {
  const {
    chunkSize = 1024 * 1024, // 1MB chunks
    maxRetries = 3,
    onProgress,
    onComplete,
    onError
  } = options

  const [isUploading, setIsUploading] = useState(false)
  const [progress, setProgress] = useState<UploadProgress>({
    progress: 0,
    uploadedBytes: 0,
    totalBytes: 0,
    speed: 0,
    timeRemaining: 0,
    currentChunk: 0,
    totalChunks: 0
  })

  const abortControllerRef = useRef<AbortController | null>(null)
  const startTimeRef = useRef<number>(0)
  const uploadedBytesRef = useRef<number>(0)

  const calculateSpeed = useCallback((uploadedBytes: number): number => {
    const elapsed = (Date.now() - startTimeRef.current) / 1000 // seconds
    return elapsed > 0 ? uploadedBytes / elapsed : 0
  }, [])

  const updateProgress = useCallback((
    currentChunk: number,
    totalChunks: number,
    totalBytes: number
  ) => {
    const uploadedBytes = currentChunk * chunkSize
    const progress = (currentChunk / totalChunks) * 100
    const speed = calculateSpeed(uploadedBytes)
    const remainingBytes = totalBytes - uploadedBytes
    const timeRemaining = speed > 0 ? remainingBytes / speed : 0

    const progressData: UploadProgress = {
      progress: Math.min(progress, 100),
      uploadedBytes,
      totalBytes,
      speed,
      timeRemaining,
      currentChunk,
      totalChunks
    }

    setProgress(progressData)
    onProgress?.(progressData)
  }, [chunkSize, calculateSpeed, onProgress])

  const uploadChunk = async (
    chunk: Blob,
    chunkIndex: number,
    totalChunks: number,
    fileName: string,
    fileType: string,
    totalSize: number,
    retryCount = 0
  ): Promise<boolean> => {
    try {
      const formData = new FormData()
      formData.append('chunk', chunk)
      formData.append('metadata', JSON.stringify({
        chunkIndex,
        totalChunks,
        fileName,
        fileType,
        totalSize,
        chunkHash: await generateChunkHash(chunk)
      }))

      const response = await fetch(`/api/app/${appId}/upload-chunk`, {
        method: 'POST',
        body: formData,
        signal: abortControllerRef.current?.signal
      })

      if (!response.ok) {
        throw new Error(`Chunk upload failed: ${response.statusText}`)
      }

      const result = await response.json()
      
      if (result.completed) {
        // Upload completed
        const uploadResult: UploadResult = {
          success: true,
          downloadUrl: result.downloadUrl,
          fileSize: result.fileSize,
          fileType: result.fileType
        }
        onComplete?.(uploadResult)
        return true
      }

      // Update progress
      updateProgress(chunkIndex + 1, totalChunks, totalSize)
      return true

    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        return false // Upload was cancelled
      }

      console.error(`Chunk ${chunkIndex} upload failed:`, error)

      // Retry logic
      if (retryCount < maxRetries) {
        console.log(`Retrying chunk ${chunkIndex}, attempt ${retryCount + 1}/${maxRetries}`)
        await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1))) // Exponential backoff
        return uploadChunk(chunk, chunkIndex, totalChunks, fileName, fileType, totalSize, retryCount + 1)
      }

      throw error
    }
  }

  const uploadFile = useCallback(async (file: File): Promise<void> => {
    if (isUploading) {
      throw new Error('Upload already in progress')
    }

    setIsUploading(true)
    startTimeRef.current = Date.now()
    uploadedBytesRef.current = 0

    // Create abort controller for cancellation
    abortControllerRef.current = new AbortController()

    try {
      const totalChunks = Math.ceil(file.size / chunkSize)
      console.log(`Starting chunked upload: ${file.name} (${file.size} bytes) in ${totalChunks} chunks`)

      // Initialize progress
      updateProgress(0, totalChunks, file.size)

      // Upload chunks sequentially (can be made parallel if needed)
      for (let chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++) {
        const start = chunkIndex * chunkSize
        const end = Math.min(start + chunkSize, file.size)
        const chunk = file.slice(start, end)

        const success = await uploadChunk(
          chunk,
          chunkIndex,
          totalChunks,
          file.name,
          file.type,
          file.size
        )

        if (!success) {
          // Upload was cancelled or failed
          return
        }
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed'
      console.error('Chunked upload error:', error)
      onError?.(errorMessage)
      
      const uploadResult: UploadResult = {
        success: false,
        error: errorMessage
      }
      onComplete?.(uploadResult)
    } finally {
      setIsUploading(false)
      abortControllerRef.current = null
    }
  }, [isUploading, chunkSize, appId, updateProgress, uploadChunk, onError, onComplete])

  const cancelUpload = useCallback(async (): Promise<void> => {
    if (!isUploading || !abortControllerRef.current) {
      return
    }

    try {
      // Abort ongoing requests
      abortControllerRef.current.abort()

      // Clean up chunks on server
      await fetch(`/api/app/${appId}/upload-chunk`, {
        method: 'DELETE'
      })

      console.log('Upload cancelled successfully')
    } catch (error) {
      console.error('Error cancelling upload:', error)
    } finally {
      setIsUploading(false)
      abortControllerRef.current = null
      
      // Reset progress
      setProgress({
        progress: 0,
        uploadedBytes: 0,
        totalBytes: 0,
        speed: 0,
        timeRemaining: 0,
        currentChunk: 0,
        totalChunks: 0
      })
    }
  }, [isUploading, appId])

  return {
    uploadFile,
    cancelUpload,
    isUploading,
    progress
  }
}

// Generate a simple hash for chunk verification
async function generateChunkHash(chunk: Blob): Promise<string> {
  const buffer = await chunk.arrayBuffer()
  const hashBuffer = await crypto.subtle.digest('SHA-256', buffer)
  const hashArray = Array.from(new Uint8Array(hashBuffer))
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
}
