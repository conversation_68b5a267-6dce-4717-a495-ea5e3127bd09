import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { writeFile, mkdir } from 'fs/promises'
import { join } from 'path'
import { existsSync } from 'fs'

export async function POST(request: NextRequest) {
  try {
    // Check API key
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Missing or invalid API key' }, { status: 401 })
    }

    const apiKey = authHeader.split(' ')[1]
    if (apiKey !== process.env.API_KEY) {
      return NextResponse.json({ error: 'Invalid API key' }, { status: 401 })
    }

    // Get user session
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse form data
    const formData = await request.formData()

    // Extract form fields
    const name = formData.get('name') as string
    const tags = JSON.parse(formData.get('tags') as string || '[]')
    const shortDescription = formData.get('shortDescription') as string
    const description = formData.get('description') as string
    const category = formData.get('category') as string
    const version = formData.get('version') as string
    const websiteUrl = formData.get('websiteUrl') as string || null
    const supportEmail = formData.get('supportEmail') as string || null
    const minVersion = formData.get('minVersion') as string || null
    const maxVersion = formData.get('maxVersion') as string || null

    // Extract files
    const iconFile = formData.get('icon') as File
    const appFile = formData.get('appFile') as File
    const screenshotFiles = formData.getAll('screenshots') as File[]

    // Validate required fields
    if (!name || !shortDescription || !description || !category || !version) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    if (!iconFile || !appFile || screenshotFiles.length < 2) {
      return NextResponse.json({ error: 'Missing required files' }, { status: 400 })
    }

    // Validate app name uniqueness for user
    const existingApp = await prisma.app.findFirst({
      where: {
        developerId: session.user.id,
        name: name
      }
    })

    if (existingApp) {
      return NextResponse.json({ error: 'App name already exists' }, { status: 400 })
    }

    // Create storage directory in public folder
    const appSlug = name.toLowerCase().replace(/[^a-z0-9]/g, '-')
    const storageDir = join(process.cwd(), 'public', 'storage', 'files', appSlug)
    if (!existsSync(storageDir)) {
      await mkdir(storageDir, { recursive: true })
    }

    const screenshotsDir = join(storageDir, 'screenshots')
    if (!existsSync(screenshotsDir)) {
      await mkdir(screenshotsDir, { recursive: true })
    }

    // Save icon
    const iconExtension = iconFile.name.split('.').pop()
    const iconPath = join(storageDir, `icon.${iconExtension}`)
    const iconBuffer = Buffer.from(await iconFile.arrayBuffer())
    await writeFile(iconPath, iconBuffer)

    // Save app file
    const appExtension = appFile.name.split('.').pop()
    const appPath = join(storageDir, `app.${appExtension}`)
    const appBuffer = Buffer.from(await appFile.arrayBuffer())
    await writeFile(appPath, appBuffer)

    // Save screenshots
    const screenshotPaths: string[] = []
    for (let i = 0; i < screenshotFiles.length; i++) {
      const screenshot = screenshotFiles[i]
      const extension = screenshot.name.split('.').pop()
      const screenshotPath = join(screenshotsDir, `screenshot-${i + 1}.${extension}`)
      const screenshotBuffer = Buffer.from(await screenshot.arrayBuffer())
      await writeFile(screenshotPath, screenshotBuffer)
      screenshotPaths.push(`/storage/files/${appSlug}/screenshots/screenshot-${i + 1}.${extension}`)
    }

    // Create app in database
    const app = await prisma.app.create({
      data: {
        developerId: session.user.id,
        name,
        description,
        shortDescription,
        version,
        category,
        tags,
        downloadUrl: `/storage/files/${appSlug}/app.${appExtension}`,
        fileSize: appFile.size,
        screenshots: screenshotPaths,
        iconUrl: `/storage/files/${appSlug}/icon.${iconExtension}`,
        website: websiteUrl,
        supportEmail,
        minVersion,
        maxVersion,
        status: 'PENDING'
      }
    })

    return NextResponse.json({
      success: true,
      appId: app.id,
      message: 'App created successfully and is pending review'
    })

  } catch (error) {
    console.error('Error creating app:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
