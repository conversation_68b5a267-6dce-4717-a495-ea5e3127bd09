import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { validateFile } from '@/lib/fileValidation'
import { virusScanningService } from '@/lib/virusScanning'
import { cloudStorageService } from '@/lib/cloudStorage'
import { appNotificationService } from '@/lib/email'

export async function POST(request: NextRequest) {
  try {
    // Check API key
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Missing or invalid API key' }, { status: 401 })
    }

    const apiKey = authHeader.split(' ')[1]
    if (apiKey !== process.env.API_KEY) {
      return NextResponse.json({ error: 'Invalid API key' }, { status: 401 })
    }

    // Get user session
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse form data
    const formData = await request.formData()

    // Extract form fields
    const name = formData.get('name') as string
    const tags = JSON.parse(formData.get('tags') as string || '[]')
    const shortDescription = formData.get('shortDescription') as string
    const description = formData.get('description') as string
    const category = formData.get('category') as string
    const version = formData.get('version') as string
    const websiteUrl = formData.get('websiteUrl') as string || null
    const supportEmail = formData.get('supportEmail') as string || null
    const minVersion = formData.get('minVersion') as string || null
    const maxVersion = formData.get('maxVersion') as string || null

    // Extract files
    const iconFile = formData.get('icon') as File
    const appFile = formData.get('appFile') as File
    const screenshotFiles = formData.getAll('screenshots') as File[]

    // Validate required fields
    if (!name || !shortDescription || !description || !category || !version) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 })
    }

    if (!iconFile || !appFile || screenshotFiles.length < 2) {
      return NextResponse.json({ error: 'Missing required files' }, { status: 400 })
    }

    // Validate app name uniqueness for user
    const existingApp = await prisma.app.findFirst({
      where: {
        developerId: session.user.id,
        name: name
      }
    })

    if (existingApp) {
      return NextResponse.json({ error: 'App name already exists' }, { status: 400 })
    }

    // Check if cloud storage is configured
    if (!cloudStorageService.isConfigured()) {
      return NextResponse.json({ error: 'Cloud storage not configured' }, { status: 500 })
    }

    console.log(`[R2 APP CREATE] Processing app: ${name} with ${screenshotFiles.length} screenshots`)

    // Create app in database first (without file URLs)
    const app = await prisma.app.create({
      data: {
        developerId: session.user.id,
        name,
        description,
        shortDescription,
        version,
        category,
        tags,
        downloadUrl: '', // Will be updated after file upload
        fileSize: appFile.size,
        screenshots: [], // Will be updated after file upload
        iconUrl: '', // Will be updated after file upload
        website: websiteUrl,
        supportEmail,
        minVersion,
        maxVersion,
        status: 'PENDING',
        pendingExpiry: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000) // 5 days from now
      }
    })

    console.log(`[R2 APP CREATE] Created app in database: ${app.id}`)

    try {
      // Upload app file to R2
      console.log(`[R2 APP CREATE] Validating and uploading app file...`)
      const appBuffer = Buffer.from(await appFile.arrayBuffer())

      // Validate app file
      const appValidationResult = await validateFile(appFile)
      if (!appValidationResult.isValid) {
        throw new Error(`App file validation failed: ${appValidationResult.errors.join(', ')}`)
      }

      // Virus scan app file
      const appScanResult = await virusScanningService.scanFile(appBuffer, appFile.name)
      if (!appScanResult.isClean) {
        throw new Error(`App file failed security scan: ${appScanResult.threats.join(', ')}`)
      }

      // Upload app file to R2
      const appStorageKey = cloudStorageService.generateFileKey(app.id, appFile.name, appValidationResult.fileType || 'unknown')
      const appUploadResult = await cloudStorageService.uploadFile(
        appBuffer,
        appStorageKey,
        appFile.type || 'application/octet-stream',
        {
          originalFileName: appFile.name,
          appId: app.id,
          fileType: appValidationResult.fileType || 'unknown',
          virusScanStatus: 'CLEAN'
        }
      )

      if (!appUploadResult.success) {
        throw new Error(`Failed to upload app file: ${appUploadResult.error}`)
      }

      console.log(`[R2 APP CREATE] App file uploaded successfully: ${appUploadResult.url}`)

      // Upload icon to R2
      console.log(`[R2 APP CREATE] Uploading icon...`)
      const iconBuffer = Buffer.from(await iconFile.arrayBuffer())
      const iconStorageKey = cloudStorageService.generateFileKey(app.id, `icon_${iconFile.name}`, 'image')

      const iconUploadResult = await cloudStorageService.uploadFile(
        iconBuffer,
        iconStorageKey,
        iconFile.type || 'image/png',
        {
          originalFileName: iconFile.name,
          appId: app.id,
          fileType: 'icon'
        }
      )

      if (!iconUploadResult.success) {
        throw new Error(`Failed to upload icon: ${iconUploadResult.error}`)
      }

      console.log(`[R2 APP CREATE] Icon uploaded successfully: ${iconUploadResult.url}`)

      // Upload screenshots to R2
      console.log(`[R2 APP CREATE] Uploading ${screenshotFiles.length} screenshots...`)
      const screenshotUrls: string[] = []

      for (let i = 0; i < screenshotFiles.length; i++) {
        const screenshot = screenshotFiles[i]
        const screenshotBuffer = Buffer.from(await screenshot.arrayBuffer())
        const screenshotStorageKey = cloudStorageService.generateFileKey(app.id, `screenshot_${i + 1}_${screenshot.name}`, 'image')

        const screenshotUploadResult = await cloudStorageService.uploadFile(
          screenshotBuffer,
          screenshotStorageKey,
          screenshot.type || 'image/png',
          {
            originalFileName: screenshot.name,
            appId: app.id,
            fileType: 'screenshot',
            index: (i + 1).toString()
          }
        )

        if (screenshotUploadResult.success) {
          screenshotUrls.push(screenshotUploadResult.url!)
          console.log(`[R2 APP CREATE] Screenshot ${i + 1} uploaded successfully`)
        } else {
          console.error(`[R2 APP CREATE] Screenshot ${i + 1} upload failed: ${screenshotUploadResult.error}`)
        }
      }

      // Update app with file URLs
      await prisma.app.update({
        where: { id: app.id },
        data: {
          downloadUrl: appUploadResult.url,
          iconUrl: iconUploadResult.url,
          screenshots: screenshotUrls,
          fileType: appValidationResult.fileType,
          originalFileName: appFile.name,
          storageKey: appUploadResult.key,
          virusScanStatus: 'CLEAN',
          virusScanResult: JSON.stringify(appScanResult)
        }
      })

      // Send notification email
      await appNotificationService.notifyAppCreated(
        session.user.id,
        app.id,
        app.name,
        session.user.email || ''
      )

      console.log(`[R2 APP CREATE] App creation completed successfully`)

      return NextResponse.json({
        success: true,
        appId: app.id,
        message: 'App created successfully and is pending review'
      })

    } catch (uploadError) {
      // If file upload fails, delete the app from database
      console.error(`[R2 APP CREATE] Upload failed, cleaning up app: ${uploadError}`)

      try {
        await prisma.app.delete({ where: { id: app.id } })
      } catch (deleteError) {
        console.error(`[R2 APP CREATE] Failed to cleanup app: ${deleteError}`)
      }

      return NextResponse.json({
        error: 'File upload failed',
        details: uploadError instanceof Error ? uploadError.message : 'Unknown error'
      }, { status: 500 })
    }

  } catch (error) {
    console.error('Error creating app:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
