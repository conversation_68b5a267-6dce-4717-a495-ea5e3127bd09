import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check API key
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Missing or invalid API key' }, { status: 401 })
    }

    const apiKey = authHeader.split(' ')[1]
    if (apiKey !== process.env.API_KEY) {
      return NextResponse.json({ error: 'Invalid API key' }, { status: 401 })
    }

    const { id: appId } = await params

    // Find the app
    const app = await prisma.app.findUnique({
      where: { id: appId },
      select: {
        id: true,
        name: true,
        downloadUrl: true,
        status: true,
        downloads: true
      }
    })

    if (!app) {
      return NextResponse.json({ error: 'App not found' }, { status: 404 })
    }

    if (app.status !== 'APPROVED') {
      return NextResponse.json({ error: 'App not available for download' }, { status: 403 })
    }

    // Increment download count
    await prisma.app.update({
      where: { id: appId },
      data: {
        downloads: {
          increment: 1
        }
      }
    })

    // Return download URL
    return NextResponse.json({
      success: true,
      downloadUrl: app.downloadUrl,
      message: 'Download initiated'
    })

  } catch (error) {
    console.error('Error processing download:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
