import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { writeFile, mkdir } from 'fs/promises'
import { join } from 'path'
import { validateFile } from '@/lib/fileValidation'
import { virusScanningService } from '@/lib/virusScanning'
import { appNotificationService } from '@/lib/email'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    // Check if app exists and belongs to user
    const existingApp = await prisma.app.findFirst({
      where: {
        id: id,
        developerId: session.user.id
      }
    })

    if (!existingApp) {
      return NextResponse.json({ error: 'App not found' }, { status: 404 })
    }

    const formData = await request.formData()
    const appFile = formData.get('appFile') as File | null
    const iconFile = formData.get('iconFile') as File | null
    const screenshotFiles = formData.getAll('screenshots') as File[]

    const uploadResults: any = {}

    // Create app directory if it doesn't exist
    const appDir = join(process.cwd(), 'public', 'storage', 'files', existingApp.name.toLowerCase().replace(/\s+/g, '-'))
    await mkdir(appDir, { recursive: true })

    // Handle app file upload with enhanced validation
    if (appFile && appFile.size > 0) {
      // Validate file type and security
      const validationResult = await validateFile(appFile)

      if (!validationResult.isValid) {
        return NextResponse.json({
          error: 'File validation failed',
          details: validationResult.errors
        }, { status: 400 })
      }

      // Get file extension from validation result
      const fileExtension = validationResult.detectedExtension || '.bin'
      const appBuffer = Buffer.from(await appFile.arrayBuffer())

      // Create both temp and permanent paths
      const tempDir = join(appDir, 'temp')
      await mkdir(tempDir, { recursive: true })

      const tempPath = join(tempDir, `app${fileExtension}`)
      const finalPath = join(appDir, `app${fileExtension}`)

      // Write to temporary location first
      await writeFile(tempPath, appBuffer)

      // Perform virus scan
      const scanResult = await virusScanningService.scanFile(appBuffer, appFile.name)

      if (!scanResult.isClean) {
        // Delete the infected file
        try {
          await import('fs/promises').then(fs => fs.unlink(tempPath))
        } catch {}

        return NextResponse.json({
          error: 'File failed security scan',
          details: scanResult.threats
        }, { status: 400 })
      }

      // Move file to final location if scan passed
      await writeFile(finalPath, appBuffer)

      // Clean up temp file
      try {
        await import('fs/promises').then(fs => fs.unlink(tempPath))
      } catch {}

      uploadResults.downloadUrl = `/storage/files/${existingApp.name.toLowerCase().replace(/\s+/g, '-')}/app${fileExtension}`
      uploadResults.fileSize = appFile.size
      uploadResults.fileType = validationResult.fileType
      uploadResults.originalFileName = appFile.name
      uploadResults.virusScanResult = scanResult
    }

    // Handle icon upload
    if (iconFile && iconFile.size > 0) {
      const iconBuffer = Buffer.from(await iconFile.arrayBuffer())
      const iconPath = join(appDir, 'icon.png')
      await writeFile(iconPath, iconBuffer)

      uploadResults.iconUrl = `/storage/files/${existingApp.name.toLowerCase().replace(/\s+/g, '-')}/icon.png`
    }

    // Handle screenshots upload
    if (screenshotFiles.length > 0) {
      const screenshotsDir = join(appDir, 'screenshots')
      await mkdir(screenshotsDir, { recursive: true })

      const screenshotUrls: string[] = []

      for (let i = 0; i < screenshotFiles.length && i < 5; i++) {
        const screenshot = screenshotFiles[i]
        if (screenshot.size > 0) {
          const screenshotBuffer = Buffer.from(await screenshot.arrayBuffer())
          const screenshotPath = join(screenshotsDir, `screenshot-${i + 1}.png`)
          await writeFile(screenshotPath, screenshotBuffer)

          screenshotUrls.push(`/storage/files/${existingApp.name.toLowerCase().replace(/\s+/g, '-')}/screenshots/screenshot-${i + 1}.png`)
        }
      }

      if (screenshotUrls.length > 0) {
        uploadResults.screenshots = screenshotUrls
      }
    }

    // Update app with new file URLs if any were uploaded
    if (Object.keys(uploadResults).length > 0) {
      const updateData: any = {}

      if (uploadResults.downloadUrl) {
        updateData.downloadUrl = uploadResults.downloadUrl
        updateData.fileSize = uploadResults.fileSize
        updateData.fileType = uploadResults.fileType
        updateData.originalFileName = uploadResults.originalFileName
        updateData.virusScanStatus = uploadResults.virusScanResult?.isClean ? 'CLEAN' : 'PENDING'
        updateData.virusScanResult = JSON.stringify(uploadResults.virusScanResult)

        // Set pending expiry date (5 days from now) if app is still pending
        if (existingApp.status === 'PENDING') {
          updateData.pendingExpiry = new Date(Date.now() + 5 * 24 * 60 * 60 * 1000)
        }
      }

      if (uploadResults.iconUrl) {
        updateData.iconUrl = uploadResults.iconUrl
      }

      if (uploadResults.screenshots) {
        updateData.screenshots = uploadResults.screenshots
      }

      const updatedApp = await prisma.app.update({
        where: { id: id },
        data: updateData
      })

      // Send notification email if this is a new upload
      if (uploadResults.downloadUrl && existingApp.status === 'PENDING') {
        const developer = await prisma.user.findUnique({
          where: { id: existingApp.developerId },
          select: { email: true, name: true }
        })

        if (developer?.email) {
          await appNotificationService.notifyAppCreated(
            existingApp.developerId,
            existingApp.id,
            existingApp.name,
            developer.email
          )
        }
      }
    }

    return NextResponse.json({
      success: true,
      uploadResults,
      message: 'Files uploaded successfully'
    })

  } catch (error) {
    console.error('Error uploading files:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}