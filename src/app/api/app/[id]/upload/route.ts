import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { validateFile } from '@/lib/fileValidation'
import { virusScanningService } from '@/lib/virusScanning'
import { appNotificationService } from '@/lib/email'
import { cloudStorageService } from '@/lib/cloudStorage'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const startTime = Date.now()
  console.log(`[UPLOAD] Starting upload process`)

  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params
    console.log(`[BACKBLAZE B2 UPLOAD] Processing upload for app ID: ${id}`)

    // Check if cloud storage is configured
    if (!cloudStorageService.isConfigured()) {
      return NextResponse.json({ error: 'Backblaze B2 storage not configured' }, { status: 500 })
    }

    // Check if app exists and belongs to user
    const existingApp = await prisma.app.findFirst({
      where: {
        id: id,
        developerId: session.user.id
      }
    })

    if (!existingApp) {
      return NextResponse.json({ error: 'App not found' }, { status: 404 })
    }

    const formData = await request.formData()
    const appFile = formData.get('appFile') as File | null
    const iconFile = formData.get('iconFile') as File | null
    const screenshotFiles = formData.getAll('screenshots') as File[]

    const uploadResults: any = {}

    // Handle app file upload with R2 cloud storage
    if (appFile && appFile.size > 0) {
      console.log(`[R2 UPLOAD] Processing app file: ${appFile.name} (${appFile.size} bytes)`)
      const fileStartTime = Date.now()

      // Validate file type and security
      console.log(`[R2 UPLOAD] Starting file validation...`)
      const validationResult = await validateFile(appFile)
      console.log(`[R2 UPLOAD] File validation completed in ${Date.now() - fileStartTime}ms`)

      if (!validationResult.isValid) {
        return NextResponse.json({
          error: 'File validation failed',
          details: validationResult.errors
        }, { status: 400 })
      }

      console.log(`[R2 UPLOAD] Converting file to buffer...`)
      const bufferStartTime = Date.now()
      const appBuffer = Buffer.from(await appFile.arrayBuffer())
      console.log(`[R2 UPLOAD] Buffer conversion completed in ${Date.now() - bufferStartTime}ms`)

      // Perform virus scan
      console.log(`[R2 UPLOAD] Starting virus scan...`)
      const scanStartTime = Date.now()
      const scanResult = await virusScanningService.scanFile(appBuffer, appFile.name)
      console.log(`[R2 UPLOAD] Virus scan completed in ${Date.now() - scanStartTime}ms`)

      if (!scanResult.isClean) {
        return NextResponse.json({
          error: 'File failed security scan',
          details: scanResult.threats
        }, { status: 400 })
      }

      // Upload to Cloudflare R2
      console.log(`[R2 UPLOAD] Uploading to cloud storage...`)
      const uploadStartTime = Date.now()
      const storageKey = cloudStorageService.generateFileKey(id, appFile.name, validationResult.fileType || 'unknown')

      const uploadResult = await cloudStorageService.uploadFile(
        appBuffer,
        storageKey,
        appFile.type || 'application/octet-stream',
        {
          originalFileName: appFile.name,
          appId: id,
          fileType: validationResult.fileType || 'unknown',
          virusScanStatus: 'CLEAN'
        }
      )

      console.log(`[R2 UPLOAD] Cloud upload completed in ${Date.now() - uploadStartTime}ms`)

      if (!uploadResult.success) {
        return NextResponse.json({
          error: 'Failed to upload to cloud storage',
          details: uploadResult.error
        }, { status: 500 })
      }

      uploadResults.downloadUrl = uploadResult.url
      uploadResults.fileSize = appFile.size
      uploadResults.fileType = validationResult.fileType
      uploadResults.originalFileName = appFile.name
      uploadResults.virusScanResult = scanResult
      uploadResults.storageKey = uploadResult.key
    }

    // Handle icon upload to R2
    if (iconFile && iconFile.size > 0) {
      console.log(`[R2 UPLOAD] Processing icon file: ${iconFile.name}`)
      const iconBuffer = Buffer.from(await iconFile.arrayBuffer())
      const iconKey = cloudStorageService.generateFileKey(id, `icon_${iconFile.name}`, 'image')

      const iconUploadResult = await cloudStorageService.uploadFile(
        iconBuffer,
        iconKey,
        iconFile.type || 'image/png',
        {
          originalFileName: iconFile.name,
          appId: id,
          fileType: 'icon'
        }
      )

      if (iconUploadResult.success) {
        uploadResults.iconUrl = iconUploadResult.url
        console.log(`[R2 UPLOAD] Icon uploaded successfully: ${iconUploadResult.url}`)
      } else {
        console.error(`[R2 UPLOAD] Icon upload failed: ${iconUploadResult.error}`)
      }
    }

    // Handle screenshots upload to R2
    if (screenshotFiles.length > 0) {
      console.log(`[R2 UPLOAD] Processing ${screenshotFiles.length} screenshot files`)
      const screenshotUrls: string[] = []

      for (let i = 0; i < screenshotFiles.length && i < 5; i++) {
        const screenshot = screenshotFiles[i]
        if (screenshot.size > 0) {
          const screenshotBuffer = Buffer.from(await screenshot.arrayBuffer())
          const screenshotKey = cloudStorageService.generateFileKey(id, `screenshot_${i + 1}_${screenshot.name}`, 'image')

          const screenshotUploadResult = await cloudStorageService.uploadFile(
            screenshotBuffer,
            screenshotKey,
            screenshot.type || 'image/png',
            {
              originalFileName: screenshot.name,
              appId: id,
              fileType: 'screenshot',
              index: (i + 1).toString()
            }
          )

          if (screenshotUploadResult.success) {
            screenshotUrls.push(screenshotUploadResult.url!)
            console.log(`[R2 UPLOAD] Screenshot ${i + 1} uploaded successfully`)
          } else {
            console.error(`[R2 UPLOAD] Screenshot ${i + 1} upload failed: ${screenshotUploadResult.error}`)
          }
        }
      }

      if (screenshotUrls.length > 0) {
        uploadResults.screenshots = screenshotUrls
      }
    }

    // Update app with new file URLs if any were uploaded
    if (Object.keys(uploadResults).length > 0) {
      const updateData: any = {}

      if (uploadResults.downloadUrl) {
        updateData.downloadUrl = uploadResults.downloadUrl
        updateData.fileSize = uploadResults.fileSize
        updateData.fileType = uploadResults.fileType
        updateData.originalFileName = uploadResults.originalFileName
        updateData.virusScanStatus = uploadResults.virusScanResult?.isClean ? 'CLEAN' : 'PENDING'
        updateData.virusScanResult = JSON.stringify(uploadResults.virusScanResult)

        // Store R2 storage key for future file management
        if (uploadResults.storageKey) {
          updateData.storageKey = uploadResults.storageKey
        }

        // Set pending expiry date (5 days from now) if app is still pending
        if (existingApp.status === 'PENDING') {
          updateData.pendingExpiry = new Date(Date.now() + 5 * 24 * 60 * 60 * 1000)
        }
      }

      if (uploadResults.iconUrl) {
        updateData.iconUrl = uploadResults.iconUrl
      }

      if (uploadResults.screenshots) {
        updateData.screenshots = uploadResults.screenshots
      }

      console.log(`[UPLOAD] Updating database...`)
      const dbStartTime = Date.now()
      await prisma.app.update({
        where: { id: id },
        data: updateData
      })
      console.log(`[UPLOAD] Database update completed in ${Date.now() - dbStartTime}ms`)

      // Send notification email if this is a new upload
      if (uploadResults.downloadUrl && existingApp.status === 'PENDING') {
        console.log(`[UPLOAD] Sending notification email...`)
        const emailStartTime = Date.now()
        const developer = await prisma.user.findUnique({
          where: { id: existingApp.developerId },
          select: { email: true, name: true }
        })

        if (developer?.email) {
          await appNotificationService.notifyAppCreated(
            existingApp.developerId,
            existingApp.id,
            existingApp.name,
            developer.email
          )
        }
        console.log(`[UPLOAD] Email notification completed in ${Date.now() - emailStartTime}ms`)
      }
    }

    const totalTime = Date.now() - startTime
    console.log(`[UPLOAD] Upload process completed successfully in ${totalTime}ms`)

    return NextResponse.json({
      success: true,
      uploadResults,
      message: 'Files uploaded successfully',
      processingTime: totalTime
    })

  } catch (error) {
    const totalTime = Date.now() - startTime
    console.error(`[UPLOAD] Upload process failed after ${totalTime}ms:`, error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}