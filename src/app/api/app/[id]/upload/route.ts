import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { writeFile, mkdir } from 'fs/promises'
import { join } from 'path'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    // Check if app exists and belongs to user
    const existingApp = await prisma.app.findFirst({
      where: {
        id: id,
        developerId: session.user.id
      }
    })

    if (!existingApp) {
      return NextResponse.json({ error: 'App not found' }, { status: 404 })
    }

    const formData = await request.formData()
    const appFile = formData.get('appFile') as File | null
    const iconFile = formData.get('iconFile') as File | null
    const screenshotFiles = formData.getAll('screenshots') as File[]

    const uploadResults: any = {}

    // Create app directory if it doesn't exist
    const appDir = join(process.cwd(), 'public', 'storage', 'files', existingApp.name.toLowerCase().replace(/\s+/g, '-'))
    await mkdir(appDir, { recursive: true })

    // Handle app file upload
    if (appFile && appFile.size > 0) {
      const appBuffer = Buffer.from(await appFile.arrayBuffer())
      const appPath = join(appDir, 'app.zip')
      await writeFile(appPath, appBuffer)
      
      uploadResults.downloadUrl = `/storage/files/${existingApp.name.toLowerCase().replace(/\s+/g, '-')}/app.zip`
      uploadResults.fileSize = appFile.size
    }

    // Handle icon upload
    if (iconFile && iconFile.size > 0) {
      const iconBuffer = Buffer.from(await iconFile.arrayBuffer())
      const iconPath = join(appDir, 'icon.png')
      await writeFile(iconPath, iconBuffer)
      
      uploadResults.iconUrl = `/storage/files/${existingApp.name.toLowerCase().replace(/\s+/g, '-')}/icon.png`
    }

    // Handle screenshots upload
    if (screenshotFiles.length > 0) {
      const screenshotsDir = join(appDir, 'screenshots')
      await mkdir(screenshotsDir, { recursive: true })
      
      const screenshotUrls: string[] = []
      
      for (let i = 0; i < screenshotFiles.length && i < 5; i++) {
        const screenshot = screenshotFiles[i]
        if (screenshot.size > 0) {
          const screenshotBuffer = Buffer.from(await screenshot.arrayBuffer())
          const screenshotPath = join(screenshotsDir, `screenshot-${i + 1}.png`)
          await writeFile(screenshotPath, screenshotBuffer)
          
          screenshotUrls.push(`/storage/files/${existingApp.name.toLowerCase().replace(/\s+/g, '-')}/screenshots/screenshot-${i + 1}.png`)
        }
      }
      
      if (screenshotUrls.length > 0) {
        uploadResults.screenshots = screenshotUrls
      }
    }

    // Update app with new file URLs if any were uploaded
    if (Object.keys(uploadResults).length > 0) {
      const updateData: any = {}
      
      if (uploadResults.downloadUrl) {
        updateData.downloadUrl = uploadResults.downloadUrl
        updateData.fileSize = uploadResults.fileSize
      }
      
      if (uploadResults.iconUrl) {
        updateData.iconUrl = uploadResults.iconUrl
      }
      
      if (uploadResults.screenshots) {
        updateData.screenshots = uploadResults.screenshots
      }
      
      await prisma.app.update({
        where: { id: id },
        data: updateData
      })
    }

    return NextResponse.json({
      success: true,
      uploadResults,
      message: 'Files uploaded successfully'
    })

  } catch (error) {
    console.error('Error uploading files:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}