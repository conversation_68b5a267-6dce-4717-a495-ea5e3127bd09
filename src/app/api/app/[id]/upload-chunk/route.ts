import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { validateFile } from '@/lib/fileValidation'
import { virusScanningService } from '@/lib/virusScanning'
import { cloudStorageService } from '@/lib/cloudStorage'
import { appNotificationService } from '@/lib/email'

interface ChunkUploadData {
  chunkIndex: number
  totalChunks: number
  fileName: string
  fileType: string
  totalSize: number
  chunkHash: string
  uploadId?: string // For multipart upload tracking
}

// In-memory storage for multipart uploads (in production, use Redis or database)
const multipartUploads = new Map<string, {
  uploadId: string
  key: string
  parts: Array<{ ETag: string; PartNumber: number }>
  fileName: string
  fileType: string
  totalSize: number
}>()

// POST - Upload a file chunk to Backblaze B2
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    // Check if cloud storage is configured
    if (!cloudStorageService.isConfigured()) {
      return NextResponse.json({ error: 'Cloud storage not configured' }, { status: 500 })
    }

    // Verify app exists and user owns it
    const existingApp = await prisma.app.findFirst({
      where: {
        id: id,
        developerId: session.user.id
      }
    })

    if (!existingApp) {
      return NextResponse.json({ error: 'App not found' }, { status: 404 })
    }

    const formData = await request.formData()
    const chunk = formData.get('chunk') as File
    const metadata = JSON.parse(formData.get('metadata') as string) as ChunkUploadData

    if (!chunk) {
      return NextResponse.json({ error: 'No chunk provided' }, { status: 400 })
    }

    console.log(`[BACKBLAZE B2 CHUNK UPLOAD] Processing chunk ${metadata.chunkIndex + 1}/${metadata.totalChunks} for app ${id}`)

    const chunkBuffer = Buffer.from(await chunk.arrayBuffer())
    const uploadKey = `${id}_${metadata.fileName}`

    // Initialize multipart upload on first chunk
    if (metadata.chunkIndex === 0) {
      const storageKey = cloudStorageService.generateFileKey(id, metadata.fileName, metadata.fileType)
      const multipartUpload = await cloudStorageService.startMultipartUpload(
        storageKey,
        metadata.fileType,
        {
          originalFileName: metadata.fileName,
          appId: id,
          totalSize: metadata.totalSize.toString()
        }
      )

      if (!multipartUpload) {
        return NextResponse.json({ error: 'Failed to start multipart upload' }, { status: 500 })
      }

      // Store multipart upload info
      multipartUploads.set(uploadKey, {
        uploadId: multipartUpload.uploadId,
        key: multipartUpload.key,
        parts: [],
        fileName: metadata.fileName,
        fileType: metadata.fileType,
        totalSize: metadata.totalSize
      })

      console.log(`[BACKBLAZE B2 CHUNK UPLOAD] Started multipart upload: ${multipartUpload.uploadId}`)
    }

    // Get multipart upload info
    const uploadInfo = multipartUploads.get(uploadKey)
    if (!uploadInfo) {
      return NextResponse.json({ error: 'Upload session not found' }, { status: 400 })
    }

    // Upload chunk to R2
    const partNumber = metadata.chunkIndex + 1
    const uploadResult = await cloudStorageService.uploadPart(
      uploadInfo.key,
      uploadInfo.uploadId,
      partNumber,
      chunkBuffer
    )

    if (!uploadResult) {
      return NextResponse.json({ error: 'Failed to upload chunk' }, { status: 500 })
    }

    // Store part info
    uploadInfo.parts.push(uploadResult)
    console.log(`[BACKBLAZE B2 CHUNK UPLOAD] Uploaded part ${partNumber} (${chunkBuffer.length} bytes)`)

    // Check if all chunks are uploaded
    const isLastChunk = metadata.chunkIndex === metadata.totalChunks - 1

    if (isLastChunk) {
      console.log(`[BACKBLAZE B2 CHUNK UPLOAD] All chunks received, completing multipart upload...`)

      // Complete multipart upload
      const completeResult = await cloudStorageService.completeMultipartUpload(
        uploadInfo.key,
        uploadInfo.uploadId,
        uploadInfo.parts
      )

      if (!completeResult.success) {
        // Clean up failed upload
        await cloudStorageService.abortMultipartUpload(uploadInfo.key, uploadInfo.uploadId)
        multipartUploads.delete(uploadKey)
        return NextResponse.json({
          error: 'Failed to complete upload',
          details: completeResult.error
        }, { status: 500 })
      }

      // Download file for validation and virus scanning
      console.log(`[BACKBLAZE B2 CHUNK UPLOAD] Downloading file for validation...`)
      const fileBuffer = await cloudStorageService.downloadFile(uploadInfo.key)

      if (!fileBuffer) {
        return NextResponse.json({ error: 'Failed to download file for validation' }, { status: 500 })
      }

      // Validate the file
      const fileForValidation = new File([fileBuffer], metadata.fileName, { type: metadata.fileType })
      const validationResult = await validateFile(fileForValidation)

      if (!validationResult.isValid) {
        // Delete invalid file from R2
        await cloudStorageService.deleteFile(uploadInfo.key)
        multipartUploads.delete(uploadKey)
        return NextResponse.json({
          error: 'File validation failed',
          details: validationResult.errors
        }, { status: 400 })
      }

      // Perform virus scan
      console.log(`[BACKBLAZE B2 CHUNK UPLOAD] Starting virus scan...`)
      const scanResult = await virusScanningService.scanFile(fileBuffer, metadata.fileName)

      if (!scanResult.isClean) {
        // Delete infected file from R2
        await cloudStorageService.deleteFile(uploadInfo.key)
        multipartUploads.delete(uploadKey)
        return NextResponse.json({
          error: 'File failed security scan',
          details: scanResult.threats
        }, { status: 400 })
      }

      // Update app in database
      await prisma.app.update({
        where: { id: id },
        data: {
          downloadUrl: completeResult.url,
          fileSize: metadata.totalSize,
          fileType: validationResult.fileType,
          originalFileName: metadata.fileName,
          virusScanStatus: 'CLEAN',
          virusScanResult: JSON.stringify(scanResult),
          pendingExpiry: existingApp.status === 'PENDING' ?
            new Date(Date.now() + 5 * 24 * 60 * 60 * 1000) : undefined
        }
      })

      // Send notification email
      if (existingApp.status === 'PENDING') {
        const developer = await prisma.user.findUnique({
          where: { id: existingApp.developerId },
          select: { email: true, name: true }
        })

        if (developer?.email) {
          await appNotificationService.notifyAppCreated(
            existingApp.developerId,
            existingApp.id,
            existingApp.name,
            developer.email
          )
        }
      }

      // Clean up multipart upload tracking
      multipartUploads.delete(uploadKey)

      console.log(`[BACKBLAZE B2 CHUNK UPLOAD] File upload completed successfully: ${completeResult.url}`)

      return NextResponse.json({
        success: true,
        completed: true,
        downloadUrl: completeResult.url,
        fileSize: metadata.totalSize,
        fileType: validationResult.fileType,
        virusScanResult: scanResult
      })
    }

    // Return progress for incomplete upload
    return NextResponse.json({
      success: true,
      completed: false,
      chunkIndex: metadata.chunkIndex,
      totalChunks: metadata.totalChunks,
      progress: ((metadata.chunkIndex + 1) / metadata.totalChunks) * 100
    })

  } catch (error) {
    console.error('Backblaze B2 chunk upload error:', error)
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// DELETE - Cancel upload and clean up Backblaze B2 multipart upload
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    // Get upload info from query params
    const url = new URL(request.url)
    const fileName = url.searchParams.get('fileName')

    if (!fileName) {
      return NextResponse.json({ error: 'fileName parameter required' }, { status: 400 })
    }

    const uploadKey = `${id}_${fileName}`
    const uploadInfo = multipartUploads.get(uploadKey)

    if (uploadInfo) {
      // Abort multipart upload in R2
      await cloudStorageService.abortMultipartUpload(uploadInfo.key, uploadInfo.uploadId)

      // Remove from tracking
      multipartUploads.delete(uploadKey)

      console.log(`[BACKBLAZE B2 CHUNK UPLOAD] Cancelled multipart upload: ${uploadInfo.uploadId}`)
    }

    return NextResponse.json({ success: true, message: 'Upload cancelled and cleaned up' })
  } catch (error) {
    console.error('Backblaze B2 upload cancellation error:', error)
    return NextResponse.json({ error: 'Failed to cancel upload' }, { status: 500 })
  }
}
