import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { writeFile, mkdir, readFile, unlink } from 'fs/promises'
import { join } from 'path'
import { validateFile } from '@/lib/fileValidation'
import { virusScanningService } from '@/lib/virusScanning'

interface ChunkUploadData {
  chunkIndex: number
  totalChunks: number
  fileName: string
  fileType: string
  totalSize: number
  chunkHash: string
}

// POST - Upload a file chunk
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = params

    // Verify app exists and user owns it
    const existingApp = await prisma.app.findFirst({
      where: {
        id: id,
        developerId: session.user.id
      }
    })

    if (!existingApp) {
      return NextResponse.json({ error: 'App not found' }, { status: 404 })
    }

    const formData = await request.formData()
    const chunk = formData.get('chunk') as File
    const metadata = JSON.parse(formData.get('metadata') as string) as ChunkUploadData

    if (!chunk) {
      return NextResponse.json({ error: 'No chunk provided' }, { status: 400 })
    }

    console.log(`[CHUNK UPLOAD] Processing chunk ${metadata.chunkIndex + 1}/${metadata.totalChunks} for app ${id}`)

    // Create upload directory
    const uploadDir = join(process.cwd(), 'temp', 'chunks', id)
    await mkdir(uploadDir, { recursive: true })

    // Save chunk to temporary location
    const chunkPath = join(uploadDir, `chunk_${metadata.chunkIndex}`)
    const chunkBuffer = Buffer.from(await chunk.arrayBuffer())
    await writeFile(chunkPath, chunkBuffer)

    console.log(`[CHUNK UPLOAD] Saved chunk ${metadata.chunkIndex} (${chunkBuffer.length} bytes)`)

    // Check if all chunks are uploaded
    const isLastChunk = metadata.chunkIndex === metadata.totalChunks - 1
    
    if (isLastChunk) {
      console.log(`[CHUNK UPLOAD] All chunks received, assembling file...`)
      
      // Assemble all chunks into final file
      const assembledFile = await assembleChunks(uploadDir, metadata)
      
      if (!assembledFile) {
        return NextResponse.json({ error: 'Failed to assemble file chunks' }, { status: 500 })
      }

      // Validate the assembled file
      const fileForValidation = new File([assembledFile], metadata.fileName, { type: metadata.fileType })
      const validationResult = await validateFile(fileForValidation)

      if (!validationResult.isValid) {
        // Clean up
        await cleanupChunks(uploadDir)
        return NextResponse.json({ 
          error: 'File validation failed', 
          details: validationResult.errors 
        }, { status: 400 })
      }

      // Perform virus scan
      console.log(`[CHUNK UPLOAD] Starting virus scan...`)
      const scanResult = await virusScanningService.scanFile(assembledFile, metadata.fileName)

      if (!scanResult.isClean) {
        // Clean up
        await cleanupChunks(uploadDir)
        return NextResponse.json({ 
          error: 'File failed security scan', 
          details: scanResult.threats 
        }, { status: 400 })
      }

      // Save final file
      const appSlug = existingApp.name.toLowerCase().replace(/\s+/g, '-')
      const appDir = join(process.cwd(), 'public', 'storage', 'files', appSlug)
      await mkdir(appDir, { recursive: true })

      const fileExtension = validationResult.detectedExtension || '.bin'
      const finalPath = join(appDir, `app${fileExtension}`)
      await writeFile(finalPath, assembledFile)

      // Update app in database
      await prisma.app.update({
        where: { id: id },
        data: {
          downloadUrl: `/storage/files/${appSlug}/app${fileExtension}`,
          fileSize: metadata.totalSize,
          fileType: validationResult.fileType,
          originalFileName: metadata.fileName,
          virusScanStatus: 'CLEAN',
          virusScanResult: JSON.stringify(scanResult),
          pendingExpiry: existingApp.status === 'PENDING' ? 
            new Date(Date.now() + 5 * 24 * 60 * 60 * 1000) : undefined
        }
      })

      // Clean up chunks
      await cleanupChunks(uploadDir)

      console.log(`[CHUNK UPLOAD] File upload completed successfully`)

      return NextResponse.json({
        success: true,
        completed: true,
        downloadUrl: `/storage/files/${appSlug}/app${fileExtension}`,
        fileSize: metadata.totalSize,
        fileType: validationResult.fileType,
        virusScanResult: scanResult
      })
    }

    // Return progress for incomplete upload
    return NextResponse.json({
      success: true,
      completed: false,
      chunkIndex: metadata.chunkIndex,
      totalChunks: metadata.totalChunks,
      progress: ((metadata.chunkIndex + 1) / metadata.totalChunks) * 100
    })

  } catch (error) {
    console.error('Chunk upload error:', error)
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// Assemble chunks into final file
async function assembleChunks(uploadDir: string, metadata: ChunkUploadData): Promise<Buffer | null> {
  try {
    const chunks: Buffer[] = []
    
    for (let i = 0; i < metadata.totalChunks; i++) {
      const chunkPath = join(uploadDir, `chunk_${i}`)
      const chunkData = await readFile(chunkPath)
      chunks.push(chunkData)
    }

    const assembledBuffer = Buffer.concat(chunks)
    
    // Verify total size matches
    if (assembledBuffer.length !== metadata.totalSize) {
      console.error(`[CHUNK ASSEMBLY] Size mismatch: expected ${metadata.totalSize}, got ${assembledBuffer.length}`)
      return null
    }

    console.log(`[CHUNK ASSEMBLY] Successfully assembled ${chunks.length} chunks into ${assembledBuffer.length} bytes`)
    return assembledBuffer

  } catch (error) {
    console.error('[CHUNK ASSEMBLY] Error assembling chunks:', error)
    return null
  }
}

// Clean up temporary chunk files
async function cleanupChunks(uploadDir: string): Promise<void> {
  try {
    const fs = await import('fs/promises')
    const entries = await fs.readdir(uploadDir)
    
    for (const entry of entries) {
      const entryPath = join(uploadDir, entry)
      await unlink(entryPath)
    }
    
    await fs.rmdir(uploadDir)
    console.log(`[CHUNK CLEANUP] Cleaned up chunks in ${uploadDir}`)
  } catch (error) {
    console.error('[CHUNK CLEANUP] Error cleaning up chunks:', error)
  }
}

// DELETE - Cancel upload and clean up chunks
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = params
    const uploadDir = join(process.cwd(), 'temp', 'chunks', id)
    
    await cleanupChunks(uploadDir)
    
    return NextResponse.json({ success: true, message: 'Upload cancelled and cleaned up' })
  } catch (error) {
    console.error('Upload cancellation error:', error)
    return NextResponse.json({ error: 'Failed to cancel upload' }, { status: 500 })
  }
}
