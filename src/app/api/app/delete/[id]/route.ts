import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { rm } from 'fs/promises'
import { join } from 'path'
import { existsSync } from 'fs'

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check API key
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Missing or invalid API key' }, { status: 401 })
    }

    const apiKey = authHeader.split(' ')[1]
    if (apiKey !== process.env.API_KEY) {
      return NextResponse.json({ error: 'Invalid API key' }, { status: 401 })
    }

    // Get user session
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id: appId } = await params

    // Find the app and verify ownership
    const app = await prisma.app.findUnique({
      where: { id: appId },
      select: {
        id: true,
        name: true,
        developerId: true
      }
    })

    if (!app) {
      return NextResponse.json({ error: 'App not found' }, { status: 404 })
    }

    if (app.developerId !== session.user.id) {
      return NextResponse.json({ error: 'Unauthorized - not app owner' }, { status: 403 })
    }

    // Delete app files
    const appSlug = app.name.toLowerCase().replace(/[^a-z0-9]/g, '-')
    const storageDir = join(process.cwd(), 'public', 'storage', 'files', appSlug)
    if (existsSync(storageDir)) {
      await rm(storageDir, { recursive: true, force: true })
    }

    // Delete app from database (this will cascade delete comments and versions)
    await prisma.app.delete({
      where: { id: appId }
    })

    return NextResponse.json({
      success: true,
      message: 'App deleted successfully'
    })

  } catch (error) {
    console.error('Error deleting app:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
