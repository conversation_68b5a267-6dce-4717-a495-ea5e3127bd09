'use client'

import { DashboardLayout } from '@/components/layout/DashboardLayout'
import { useAuth } from '@/hooks/useAuth'
import { useState, useEffect } from 'react'
import {
  Package,
  Plus,
  Edit,
  Trash2,
  Download,
  Eye,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react'
import { CreateAppModal } from '@/components/apps/CreateAppModal'
import { EditAppModal } from '@/components/apps/EditAppModal'

interface App {
  id: string
  name: string
  status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'SUSPENDED'
  downloads: number
  version: string
  iconUrl?: string
  shortDescription?: string
  createdAt: string
  updatedAt: string
}

export default function AppsPage() {
  const { user } = useAuth()
  const [apps, setApps] = useState<App[]>([])
  const [loading, setLoading] = useState(true)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [editingAppId, setEditingAppId] = useState<string | null>(null)

  useEffect(() => {
    fetchUserApps()
  }, [])

  const fetchUserApps = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/user/apps', {
        headers: {
          'Authorization': `Bearer ${process.env.NEXT_PUBLIC_API_KEY}`
        }
      })
      if (response.ok) {
        const data = await response.json()
        setApps(data.apps)
      }
    } catch (error) {
      console.error('Failed to fetch apps:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return <CheckCircle className="w-4 h-4 text-emerald-400" />
      case 'PENDING':
        return <Clock className="w-4 h-4 text-amber-400" />
      case 'REJECTED':
        return <XCircle className="w-4 h-4 text-red-400" />
      case 'SUSPENDED':
        return <AlertCircle className="w-4 h-4 text-orange-400" />
      default:
        return <Clock className="w-4 h-4 text-zinc-400" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return 'bg-emerald-500/20 text-emerald-400 border-emerald-500/30'
      case 'PENDING':
        return 'bg-amber-500/20 text-amber-400 border-amber-500/30'
      case 'REJECTED':
        return 'bg-red-500/20 text-red-400 border-red-500/30'
      case 'SUSPENDED':
        return 'bg-orange-500/20 text-orange-400 border-orange-500/30'
      default:
        return 'bg-zinc-500/20 text-zinc-400 border-zinc-500/30'
    }
  }

  const handleEditApp = (appId: string) => {
    setEditingAppId(appId)
    setShowEditModal(true)
  }

  const handleDeleteApp = async (appId: string) => {
    if (!confirm('Are you sure you want to delete this app? This action cannot be undone.')) {
      return
    }

    try {
      const response = await fetch(`/api/app/${appId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        setApps(apps.filter(app => app.id !== appId))
      } else {
        alert('Failed to delete app')
      }
    } catch (error) {
      console.error('Failed to delete app:', error)
      alert('Failed to delete app')
    }
  }

  return (
    <DashboardLayout>
      <div className="space-y-6 sm:space-y-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-to-r from-white to-zinc-300 bg-clip-text text-transparent">
              My Applications
            </h1>
            <p className="text-zinc-400 mt-2">
              Manage your apps, track downloads, and monitor approval status
            </p>
          </div>
          <button
            onClick={() => setShowCreateModal(true)}
            className="flex items-center space-x-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium py-3 px-6 rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl glow-blue"
          >
            <Plus className="w-5 h-5" />
            <span>Create New App</span>
          </button>
        </div>

        {/* Apps Grid */}
        {loading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
            {[1, 2, 3].map((i) => (
              <div key={i} className="glass rounded-xl p-6 border border-zinc-800/50 animate-pulse">
                <div className="w-16 h-16 bg-zinc-700 rounded-xl mb-4"></div>
                <div className="h-4 bg-zinc-700 rounded mb-2"></div>
                <div className="h-3 bg-zinc-700 rounded w-2/3"></div>
              </div>
            ))}
          </div>
        ) : apps.length === 0 ? (
          <div className="text-center py-12">
            <div className="w-24 h-24 bg-gradient-to-br from-blue-500/20 to-purple-600/20 rounded-full flex items-center justify-center mx-auto mb-6">
              <Package className="w-12 h-12 text-blue-400" />
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">No apps yet</h3>
            <p className="text-zinc-400 mb-6">Create your first app to get started</p>
            <button
              onClick={() => setShowCreateModal(true)}
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium py-3 px-6 rounded-xl transition-all duration-200"
            >
              Create Your First App
            </button>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
            {apps.map((app) => (
              <div key={app.id} className="group glass rounded-xl p-6 border border-zinc-800/50 hover:border-zinc-700/50 transition-all duration-200 hover-lift">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                      {app.iconUrl ? (
                        <img src={app.iconUrl} alt={app.name} className="w-12 h-12 rounded-xl object-cover" />
                      ) : (
                        <Package className="w-6 h-6 text-white" />
                      )}
                    </div>
                    <div>
                      <h3 className="font-semibold text-white truncate">{app.name}</h3>
                      <p className="text-sm text-zinc-400">v{app.version}</p>
                    </div>
                  </div>
                  <div className={`flex items-center space-x-1 px-2 py-1 rounded-full border text-xs font-medium ${getStatusColor(app.status)}`}>
                    {getStatusIcon(app.status)}
                    <span>{app.status}</span>
                  </div>
                </div>

                {app.shortDescription && (
                  <p className="text-sm text-zinc-400 mb-4 line-clamp-2">{app.shortDescription}</p>
                )}

                <div className="flex items-center justify-between text-sm text-zinc-400 mb-4">
                  <div className="flex items-center space-x-1">
                    <Download className="w-4 h-4" />
                    <span>{app.downloads.toLocaleString()} downloads</span>
                  </div>
                  <span>Updated {new Date(app.updatedAt).toLocaleDateString()}</span>
                </div>

                <div className="flex items-center space-x-2">
                  <button className="flex-1 flex items-center justify-center space-x-2 py-2 px-3 bg-zinc-900/50 hover:bg-zinc-800/50 border border-zinc-800/50 hover:border-zinc-700/50 rounded-lg transition-all duration-200 text-zinc-300 hover:text-white">
                    <Eye className="w-4 h-4" />
                    <span>View</span>
                  </button>
                  <button
                    onClick={() => handleEditApp(app.id)}
                    className="flex items-center justify-center p-2 bg-zinc-900/50 hover:bg-zinc-800/50 border border-zinc-800/50 hover:border-zinc-700/50 rounded-lg transition-all duration-200 text-zinc-300 hover:text-white"
                    title="Update app"
                  >
                    <Edit className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => handleDeleteApp(app.id)}
                    className="flex items-center justify-center p-2 bg-red-500/10 hover:bg-red-500/20 border border-red-500/20 hover:border-red-500/30 rounded-lg transition-all duration-200 text-red-400 hover:text-red-300"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Create App Modal */}
      {showCreateModal && (
        <CreateAppModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          onSuccess={() => {
            setShowCreateModal(false)
            fetchUserApps()
          }}
        />
      )}

      {/* Edit App Modal */}
      {showEditModal && editingAppId && (
        <EditAppModal
          isOpen={showEditModal}
          onClose={() => {
            setShowEditModal(false)
            setEditingAppId(null)
          }}
          onSuccess={() => {
            setShowEditModal(false)
            setEditingAppId(null)
            fetchUserApps()
          }}
          appId={editingAppId}
        />
      )}
    </DashboardLayout>
  )
}
