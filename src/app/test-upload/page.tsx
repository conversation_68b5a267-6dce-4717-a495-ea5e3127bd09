'use client'

import { useState } from 'react'
import { EnhancedFileUpload } from '@/components/upload/EnhancedFileUpload'
import { useUploadGuard } from '@/hooks/useUploadGuard'
import { UploadResult } from '@/hooks/useChunkedUpload'

export default function TestUploadPage() {
  const [isUploading, setIsUploading] = useState(false)
  const [uploadResult, setUploadResult] = useState<UploadResult | null>(null)

  // Prevent navigation during upload
  useUploadGuard({ isUploading })

  const handleUploadComplete = (result: UploadResult) => {
    setIsUploading(false)
    setUploadResult(result)
    console.log('Upload completed:', result)
  }

  const handleUploadError = (error: string) => {
    setIsUploading(false)
    console.error('Upload error:', error)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-zinc-900 via-zinc-800 to-zinc-900 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            Enhanced File Upload Test
          </h1>
          <p className="text-zinc-400 text-lg">
            Test the new chunked upload system with progress tracking
          </p>
        </div>

        <div className="bg-zinc-900/50 border border-zinc-700 rounded-xl p-8">
          <h2 className="text-2xl font-semibold text-white mb-6">Upload Test File</h2>
          
          <EnhancedFileUpload
            appId="test-app-id"
            onUploadComplete={handleUploadComplete}
            onUploadError={handleUploadError}
            maxFileSize={500 * 1024 * 1024} // 500MB for testing
          />

          {/* Upload Result Display */}
          {uploadResult && (
            <div className="mt-8 p-6 bg-zinc-800/50 border border-zinc-700 rounded-lg">
              <h3 className="text-lg font-semibold text-white mb-4">Upload Result</h3>
              <pre className="text-zinc-300 text-sm overflow-x-auto">
                {JSON.stringify(uploadResult, null, 2)}
              </pre>
            </div>
          )}

          {/* Feature List */}
          <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-zinc-800/30 border border-zinc-700 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-white mb-4">✨ Enhanced Features</h3>
              <ul className="space-y-2 text-zinc-300 text-sm">
                <li>• Real-time progress tracking</li>
                <li>• Upload speed monitoring</li>
                <li>• Time remaining estimation</li>
                <li>• Chunked upload for reliability</li>
                <li>• Automatic retry on failure</li>
                <li>• Cancel upload functionality</li>
                <li>• Navigation guard protection</li>
              </ul>
            </div>

            <div className="bg-zinc-800/30 border border-zinc-700 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-white mb-4">🔒 Security Features</h3>
              <ul className="space-y-2 text-zinc-300 text-sm">
                <li>• File type validation</li>
                <li>• Magic number verification</li>
                <li>• Virus scanning integration</li>
                <li>• Size limit enforcement</li>
                <li>• Suspicious filename detection</li>
                <li>• Temporary file quarantine</li>
                <li>• Chunk integrity verification</li>
              </ul>
            </div>
          </div>

          {/* Performance Tips */}
          <div className="mt-8 bg-blue-900/20 border border-blue-700 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-4">🚀 Performance Optimizations</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <h4 className="font-medium text-blue-300 mb-2">Chunked Uploads</h4>
                <p className="text-zinc-300">
                  Files are split into 1MB chunks for better reliability and progress tracking.
                </p>
              </div>
              <div>
                <h4 className="font-medium text-blue-300 mb-2">Retry Logic</h4>
                <p className="text-zinc-300">
                  Failed chunks are automatically retried up to 3 times with exponential backoff.
                </p>
              </div>
              <div>
                <h4 className="font-medium text-blue-300 mb-2">Optimized Processing</h4>
                <p className="text-zinc-300">
                  Server-side processing is optimized with detailed performance logging.
                </p>
              </div>
            </div>
          </div>

          {/* Test Instructions */}
          <div className="mt-8 bg-yellow-900/20 border border-yellow-700 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-4">📋 Testing Instructions</h3>
            <ol className="space-y-2 text-zinc-300 text-sm list-decimal list-inside">
              <li>Select a large file (10MB+ recommended) to see progress tracking</li>
              <li>Watch the real-time progress bar and upload statistics</li>
              <li>Try canceling the upload mid-process to test cancellation</li>
              <li>Test with different file types to verify validation</li>
              <li>Check browser console for detailed performance logs</li>
              <li>Try navigating away during upload to test navigation guard</li>
            </ol>
          </div>

          {/* Supported File Types */}
          <div className="mt-8 bg-zinc-800/30 border border-zinc-700 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-4">📁 Supported File Types</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <h4 className="font-medium text-green-300 mb-2">Archives</h4>
                <p className="text-zinc-300">.zip, .rar, .7z</p>
                <p className="text-zinc-500 text-xs">Max: 500MB</p>
              </div>
              <div>
                <h4 className="font-medium text-purple-300 mb-2">Executables</h4>
                <p className="text-zinc-300">.exe, .msi, .dmg</p>
                <p className="text-zinc-500 text-xs">Max: 1GB</p>
              </div>
              <div>
                <h4 className="font-medium text-blue-300 mb-2">Mobile Apps</h4>
                <p className="text-zinc-300">.apk, .ipa</p>
                <p className="text-zinc-500 text-xs">Max: 500MB</p>
              </div>
              <div>
                <h4 className="font-medium text-orange-300 mb-2">Linux Packages</h4>
                <p className="text-zinc-300">.deb, .rpm, .appimage</p>
                <p className="text-zinc-500 text-xs">Max: 500MB</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
