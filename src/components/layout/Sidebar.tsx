'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { signOut } from 'next-auth/react'
import { useTranslations } from 'next-intl'
import {
  Home,
  Package,
  Settings,
  Users,
  BarChart3,
  Code,
  FileText,
  Shield,
  LogOut
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { useAuth } from '@/hooks/useAuth'
import { Avatar } from '@/components/ui/Avatar'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { useState } from 'react'

interface SidebarProps {
  isOpen: boolean
  onClose: () => void
}

export function Sidebar({ isOpen, onClose }: SidebarProps) {
  const pathname = usePathname()
  const { user } = useAuth()
  const t = useTranslations()
  const [isSigningOut, setIsSigningOut] = useState(false)

  const navigation = [
    { name: t('navigation.dashboard'), href: '/', icon: Home },
    { name: t('navigation.apps'), href: '/apps', icon: Package },
    { name: t('navigation.analytics'), href: '/analytics', icon: BarChart3 },
    { name: t('navigation.docs'), href: '/docs', icon: FileText },
    { name: t('navigation.apiKeys'), href: '/api-keys', icon: Code },
    { name: t('navigation.settings'), href: '/settings', icon: Settings },
  ]

  const adminNavigation = [
    { name: 'Admin Panel', href: '/admin', icon: Shield },
    { name: 'All Users', href: '/admin/users', icon: Users },
    { name: 'App Reviews', href: '/admin/reviews', icon: Package },
  ]

  const handleSignOut = async () => {
    try {
      setIsSigningOut(true)
      await signOut({
        callbackUrl: '/login',
        redirect: true
      })
    } catch (error) {
      console.error('Sign out error:', error)
      setIsSigningOut(false)
    }
  }

  const handleLinkClick = () => {
    if (window.innerWidth < 1024) {
      onClose()
    }
  }

  return (
    <div className={`
      fixed lg:static inset-y-0 left-0 z-50 w-64 flex flex-col bg-black border-r border-zinc-800/50 transform transition-transform duration-300 ease-in-out lg:transform-none
      ${isOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
    `}>
      {/* Logo */}
      <div className="flex h-16 items-center px-6 border-b border-zinc-800/50">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 rounded-lg flex items-center justify-center shadow-lg glow-blue">
            <Code className="w-5 h-5 text-white" />
          </div>
          <span className="text-xl font-bold bg-gradient-to-r from-white to-zinc-300 bg-clip-text text-transparent">AveHub</span>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4 py-6 space-y-1">
        {navigation.map((item) => {
          const isActive = pathname === item.href
          return (
            <Link
              key={item.name}
              href={item.href}
              onClick={handleLinkClick}
              className={cn(
                'flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 group hover-lift',
                isActive
                  ? 'bg-gradient-to-r from-blue-600/20 to-indigo-600/20 text-white border border-blue-500/30 glow-blue'
                  : 'text-zinc-400 hover:bg-zinc-900/50 hover:text-white hover:border-zinc-700/50 border border-transparent'
              )}
            >
              <item.icon className={cn(
                "w-5 h-5 mr-3 transition-colors",
                isActive ? "text-blue-400" : "text-zinc-500 group-hover:text-zinc-300"
              )} />
              {item.name}
            </Link>
          )
        })}

        {/* Admin Section */}
        {user?.admin && (
          <>
            <div className="pt-6 pb-3">
              <h3 className="px-3 text-xs font-semibold text-zinc-500 uppercase tracking-wider">
                Administration
              </h3>
            </div>
            {adminNavigation.map((item) => {
              const isActive = pathname === item.href
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  onClick={handleLinkClick}
                  className={cn(
                    'flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 group hover-lift',
                    isActive
                      ? 'bg-gradient-to-r from-purple-600/20 to-indigo-600/20 text-white border border-purple-500/30 glow-purple'
                      : 'text-zinc-400 hover:bg-zinc-900/50 hover:text-white hover:border-zinc-700/50 border border-transparent'
                  )}
                >
                  <item.icon className={cn(
                    "w-5 h-5 mr-3 transition-colors",
                    isActive ? "text-purple-400" : "text-zinc-500 group-hover:text-zinc-300"
                  )} />
                  {item.name}
                </Link>
              )
            })}
          </>
        )}
      </nav>

      {/* User Info */}
      <div className="p-4 border-t border-zinc-800/50 space-y-3">
        <div className="flex items-center space-x-3 p-3 rounded-xl bg-zinc-900/30 border border-zinc-800/50 hover:bg-zinc-900/50 transition-all duration-200">
          <Avatar
            src={user?.image}
            name={user?.name}
            alt={user?.name || 'User'}
            size="md"
          />
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-white truncate">
              {user?.name}
            </p>
            <p className="text-xs text-zinc-400 truncate">
              {user?.email}
            </p>
          </div>
        </div>

        {/* Sign Out Button */}
        <button
          onClick={handleSignOut}
          disabled={isSigningOut}
          className="w-full flex items-center space-x-3 p-3 rounded-xl bg-red-500/10 border border-red-500/20 hover:bg-red-500/20 hover:border-red-500/30 text-red-400 hover:text-red-300 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed group"
        >
          {isSigningOut ? (
            <LoadingSpinner size="sm" className="text-red-400" />
          ) : (
            <LogOut className="w-4 h-4" />
          )}
          <span className="text-sm font-medium">
            {isSigningOut ? t('common.saving') : t('auth.signOut')}
          </span>
        </button>
      </div>
    </div>
  )
}
