# Cloudflare R2 Setup Guide for AveHub Developer Portal

This guide will help you set up Cloudflare R2 storage for the AveHub Developer Portal to handle file uploads efficiently within Vercel's serverless constraints.

## 🌟 Why Cloudflare R2?

- **10GB Free Storage** - Perfect for app files, icons, and screenshots
- **S3-Compatible API** - Easy integration with existing tools
- **Global CDN** - Fast downloads worldwide
- **No Egress Fees** - Unlike AWS S3, R2 doesn't charge for downloads
- **Vercel Optimized** - Works perfectly with serverless functions

## 📋 Prerequisites

1. Cloudflare account (free tier available)
2. Domain name (optional, for custom URLs)
3. Vercel account for deployment

## 🚀 Step 1: Create Cloudflare R2 Bucket

### 1.1 Enable R2 in Cloudflare Dashboard
1. Log into your [Cloudflare Dashboard](https://dash.cloudflare.com)
2. Navigate to **R2 Object Storage** in the sidebar
3. Click **Create bucket**
4. Choose a bucket name: `avehub-storage` (or your preferred name)
5. Select a location close to your users
6. Click **Create bucket**

### 1.2 Configure Bucket Settings
1. Go to your bucket settings
2. Enable **Public Access** for file downloads
3. Configure **CORS** settings:
```json
[
  {
    "AllowedOrigins": ["*"],
    "AllowedMethods": ["GET", "PUT", "POST", "DELETE", "HEAD"],
    "AllowedHeaders": ["*"],
    "ExposeHeaders": ["ETag"],
    "MaxAgeSeconds": 3600
  }
]
```

## 🔑 Step 2: Create API Tokens

### 2.1 Create R2 API Token
1. Go to **My Profile** → **API Tokens**
2. Click **Create Token**
3. Use **Custom token** template
4. Configure permissions:
   - **Zone:Zone:Read** (if using custom domain)
   - **Zone:Zone Settings:Edit** (if using custom domain)
   - **Account:Cloudflare R2:Edit**
5. Set **Account Resources** to your account
6. Set **Zone Resources** to your domain (if applicable)
7. Click **Continue to summary** → **Create Token**
8. **Save the token securely** - you won't see it again!

### 2.2 Get Account ID
1. In Cloudflare Dashboard, go to the right sidebar
2. Copy your **Account ID**

## 🌐 Step 3: Set Up Custom Domain (Optional but Recommended)

### 3.1 Create Custom Domain
1. In your R2 bucket settings, go to **Settings** → **Custom Domains**
2. Click **Connect Domain**
3. Enter your subdomain: `files.yourdomain.com`
4. Follow the DNS setup instructions

### 3.2 Configure DNS
Add a CNAME record in your domain's DNS:
```
Name: files
Target: your-bucket-name.your-account-id.r2.cloudflarestorage.com
```

## ⚙️ Step 4: Configure Environment Variables

Add these variables to your `.env` file and Vercel environment:

```bash
# Cloudflare R2 Configuration
R2_ENDPOINT="https://your-account-id.r2.cloudflarestorage.com"
R2_REGION="auto"
R2_ACCESS_KEY_ID="your-r2-access-key-id"
R2_SECRET_ACCESS_KEY="your-r2-secret-access-key"
R2_BUCKET_NAME="avehub-storage"
R2_PUBLIC_URL="https://files.yourdomain.com"  # or use R2 direct URL
```

### How to get R2 credentials:
1. Go to **R2** → **Manage R2 API tokens**
2. Click **Create API token**
3. Select **Admin Read & Write** permissions
4. Copy the **Access Key ID** and **Secret Access Key**

## 🔧 Step 5: Update Vercel Environment Variables

1. Go to your Vercel project dashboard
2. Navigate to **Settings** → **Environment Variables**
3. Add all the R2 environment variables
4. Deploy your application

## 📊 Step 6: Database Migration

Run the database migration to add the storage key field:

```bash
npx prisma db push
```

This adds the `storageKey` field to track R2 file locations.

## 🧪 Step 7: Test the Setup

1. Deploy your application to Vercel
2. Go to the test upload page: `/test-upload`
3. Try uploading a large file (50MB+) to test chunked uploads
4. Verify files appear in your R2 bucket
5. Test download speeds from the R2 URLs

## 📈 Step 8: Monitor Usage

### R2 Dashboard
- Monitor storage usage in Cloudflare R2 dashboard
- Track bandwidth and request metrics
- Set up billing alerts if needed

### Free Tier Limits
- **Storage**: 10GB/month
- **Class A Operations**: 1M/month (PUT, POST, LIST)
- **Class B Operations**: 10M/month (GET, HEAD)
- **Egress**: Free (no charges for downloads)

## 🔒 Security Best Practices

### 1. Bucket Security
- Use least-privilege API tokens
- Enable bucket-level access controls
- Regularly rotate API keys

### 2. File Validation
- All files are validated before upload
- Virus scanning is performed on all uploads
- Malicious files are automatically deleted

### 3. Access Control
- Only authenticated users can upload
- Files are organized by app ID
- Temporary URLs for sensitive operations

## 🚨 Troubleshooting

### Common Issues

**1. Upload Fails with 403 Error**
- Check API token permissions
- Verify bucket name in environment variables
- Ensure CORS is configured correctly

**2. Files Not Accessible**
- Verify custom domain DNS settings
- Check bucket public access settings
- Confirm R2_PUBLIC_URL is correct

**3. Slow Upload Speeds**
- Check your internet connection
- Verify R2 region selection
- Monitor Cloudflare status page

**4. Vercel Timeout Issues**
- Chunked uploads should prevent timeouts
- Check function execution logs
- Verify environment variables in Vercel

### Debug Mode
Enable debug logging by adding:
```bash
DEBUG_R2_UPLOADS="true"
```

## 📞 Support

If you encounter issues:

1. Check Cloudflare R2 documentation
2. Review Vercel deployment logs
3. Test with smaller files first
4. Verify all environment variables are set

## 🎉 Success!

Once configured, your AveHub Developer Portal will:
- ✅ Upload files directly to Cloudflare R2
- ✅ Support chunked uploads for large files
- ✅ Provide real-time progress tracking
- ✅ Work within Vercel's 60-second timeout
- ✅ Deliver files via global CDN
- ✅ Scale to handle any file size

Your users will experience fast, reliable file uploads and downloads! 🚀
